"""
Docling service for document processing.
Converts documents to JSON and Markdown formats.
"""
import logging
import asyncio
from typing import Optional, Dict, Any
from pathlib import Path
import time

from config.settings import DoclingSettings
from models.data_models import DoclingResult


logger = logging.getLogger(__name__)


class DoclingService:
    """Service for processing documents with <PERSON>ling."""
    
    def __init__(self, settings: DoclingSettings):
        self.settings = settings
        self.enabled = settings.enabled

    async def process_document(self, file_path: str) -> DoclingResult:
        """Process a document using Docling."""
        if not self.enabled:
            return DoclingResult(
                success=False,
                error_message="Docling service is disabled"
            )
        
        start_time = time.time()
        
        try:
            # Import docling here to avoid import errors if not installed
            from docling.document_converter import DocumentConverter
            from docling.datamodel.base_models import InputFormat
            
            # Run in thread pool since docling is sync
            loop = asyncio.get_event_loop()
            
            def _process_sync():
                converter = DocumentConverter()
                result = converter.convert(file_path)
                return result
            
            # Process document
            doc_result = await loop.run_in_executor(None, _process_sync)
            
            processing_time = time.time() - start_time
            
            # Always extract both markdown and JSON content for comprehensive storage
            # This ensures we have both formats available regardless of the primary output format
            markdown_content = doc_result.document.export_to_markdown()
            json_content = doc_result.document.export_to_dict()

            # Log what content was extracted
            logger.info(f"Extracted content: markdown={len(markdown_content) if markdown_content else 0} chars, "
                       f"json={len(str(json_content)) if json_content else 0} chars")
            
            # Extract metadata
            metadata = {
                "page_count": len(doc_result.document.pages) if hasattr(doc_result.document, 'pages') else 0,
                "input_format": str(doc_result.input.format) if hasattr(doc_result, 'input') else "unknown",
                "processing_time": processing_time
            }
            
            logger.info(f"Successfully processed document: {file_path}")
            
            return DoclingResult(
                success=True,
                markdown_content=markdown_content,
                json_content=json_content,
                processing_time=processing_time,
                metadata=metadata
            )
            
        except ImportError:
            error_msg = "Docling library not installed. Please install with: pip install docling"
            logger.error(error_msg)
            return DoclingResult(
                success=False,
                error_message=error_msg
            )
            
        except Exception as e:
            error_msg = f"Error processing document {file_path}: {str(e)}"
            logger.error(error_msg)
            return DoclingResult(
                success=False,
                error_message=error_msg,
                processing_time=time.time() - start_time
            )

    async def process_document_from_bytes(self, file_data: bytes, 
                                        filename: str) -> DoclingResult:
        """Process a document from bytes data."""
        if not self.enabled:
            return DoclingResult(
                success=False,
                error_message="Docling service is disabled"
            )
        
        try:
            # Create temporary file
            import tempfile
            
            with tempfile.NamedTemporaryFile(suffix=Path(filename).suffix, delete=False) as tmp_file:
                tmp_file.write(file_data)
                tmp_path = tmp_file.name
            
            try:
                # Process the temporary file
                result = await self.process_document(tmp_path)
                return result
            finally:
                # Clean up temporary file
                Path(tmp_path).unlink(missing_ok=True)
                
        except Exception as e:
            error_msg = f"Error processing document from bytes: {str(e)}"
            logger.error(error_msg)
            return DoclingResult(
                success=False,
                error_message=error_msg
            )

    def is_supported_format(self, file_path: str) -> bool:
        """Check if the file format is supported by Docling."""
        supported_extensions = {
            '.pdf', '.docx', '.doc', '.pptx', '.ppt', 
            '.xlsx', '.xls', '.html', '.htm', '.txt', '.md'
        }
        
        file_extension = Path(file_path).suffix.lower()
        return file_extension in supported_extensions

    async def get_supported_formats(self) -> list[str]:
        """Get list of supported file formats."""
        return [
            'pdf', 'docx', 'doc', 'pptx', 'ppt',
            'xlsx', 'xls', 'html', 'htm', 'txt', 'md'
        ]

    async def health_check(self) -> bool:
        """Check if Docling service is healthy."""
        if not self.enabled:
            return False
            
        try:
            # Try to import docling
            from docling.document_converter import DocumentConverter
            return True
        except ImportError:
            logger.warning("Docling library not available")
            return False
        except Exception as e:
            logger.error(f"Docling health check failed: {e}")
            return False
