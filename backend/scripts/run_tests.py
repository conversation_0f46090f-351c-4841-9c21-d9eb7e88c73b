#!/usr/bin/env python3
"""
Test runner script for the RDF Agent System.
Provides different test execution modes and cleanup options.
"""
import asyncio
import subprocess
import sys
import argparse
from pathlib import Path
from typing import List, Optional

# Add backend to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from scripts.cleanup_test_data import cleanup_all


def run_command(cmd: List[str], cwd: Optional[Path] = None) -> int:
    """Run a command and return the exit code."""
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(cmd, cwd=cwd)
    return result.returncode


def run_unit_tests(verbose: bool = False, coverage: bool = True) -> int:
    """Run unit tests."""
    print("🧪 Running Unit Tests...")
    
    cmd = ["uv", "run", "pytest", "tests/unit/"]
    
    if verbose:
        cmd.append("-v")
    
    if coverage:
        cmd.extend(["--cov=.", "--cov-report=term-missing"])
    
    cmd.extend(["-m", "unit"])
    
    return run_command(cmd, cwd=Path(__file__).parent.parent)


def run_integration_tests(verbose: bool = False, cleanup_before: bool = True) -> int:
    """Run integration tests."""
    print("🔗 Running Integration Tests...")
    
    if cleanup_before:
        print("🧹 Cleaning up test data before integration tests...")
        asyncio.run(cleanup_all())
    
    cmd = ["uv", "run", "pytest", "tests/integration/"]
    
    if verbose:
        cmd.append("-v")
    
    cmd.extend(["-m", "integration", "--timeout=300"])
    
    return run_command(cmd, cwd=Path(__file__).parent.parent)


def run_e2e_tests(verbose: bool = False, cleanup_before: bool = True) -> int:
    """Run end-to-end tests."""
    print("🎯 Running End-to-End Tests...")
    
    if cleanup_before:
        print("🧹 Cleaning up test data before E2E tests...")
        asyncio.run(cleanup_all())
    
    cmd = ["uv", "run", "pytest", "tests/e2e/"]
    
    if verbose:
        cmd.append("-v")
    
    cmd.extend(["-m", "e2e", "--timeout=600"])
    
    return run_command(cmd, cwd=Path(__file__).parent.parent)


def run_all_tests(verbose: bool = False, cleanup_before: bool = True) -> int:
    """Run all tests in sequence."""
    print("🚀 Running All Tests...")
    
    if cleanup_before:
        print("🧹 Cleaning up test data before all tests...")
        asyncio.run(cleanup_all())
    
    # Run unit tests first (fastest)
    print("\n" + "="*50)
    unit_result = run_unit_tests(verbose=verbose, coverage=False)
    
    if unit_result != 0:
        print("❌ Unit tests failed. Stopping test execution.")
        return unit_result
    
    # Run integration tests
    print("\n" + "="*50)
    integration_result = run_integration_tests(verbose=verbose, cleanup_before=False)
    
    if integration_result != 0:
        print("❌ Integration tests failed. Continuing with E2E tests...")
    
    # Run E2E tests
    print("\n" + "="*50)
    e2e_result = run_e2e_tests(verbose=verbose, cleanup_before=False)
    
    # Summary
    print("\n" + "="*50)
    print("📊 Test Results Summary:")
    print(f"Unit Tests: {'✅ PASSED' if unit_result == 0 else '❌ FAILED'}")
    print(f"Integration Tests: {'✅ PASSED' if integration_result == 0 else '❌ FAILED'}")
    print(f"E2E Tests: {'✅ PASSED' if e2e_result == 0 else '❌ FAILED'}")
    
    # Return non-zero if any tests failed
    return max(unit_result, integration_result, e2e_result)


def run_specific_test(test_path: str, verbose: bool = False) -> int:
    """Run a specific test file or test function."""
    print(f"🎯 Running Specific Test: {test_path}")
    
    cmd = ["uv", "run", "pytest", test_path]
    
    if verbose:
        cmd.append("-v")
    
    return run_command(cmd, cwd=Path(__file__).parent.parent)


def run_tests_with_markers(markers: List[str], verbose: bool = False) -> int:
    """Run tests with specific markers."""
    print(f"🏷️ Running Tests with Markers: {', '.join(markers)}")
    
    cmd = ["uv", "run", "pytest"]
    
    for marker in markers:
        cmd.extend(["-m", marker])
    
    if verbose:
        cmd.append("-v")
    
    return run_command(cmd, cwd=Path(__file__).parent.parent)


def run_coverage_report() -> int:
    """Generate comprehensive coverage report."""
    print("📈 Generating Coverage Report...")
    
    cmd = [
        "uv", "run", "pytest", 
        "--cov=.", 
        "--cov-report=html:htmlcov",
        "--cov-report=term-missing",
        "--cov-report=xml:coverage.xml",
        "tests/"
    ]
    
    result = run_command(cmd, cwd=Path(__file__).parent.parent)
    
    if result == 0:
        print("📊 Coverage report generated:")
        print("  - HTML: htmlcov/index.html")
        print("  - XML: coverage.xml")
    
    return result


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Test runner for RDF Agent System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_tests.py unit                    # Run unit tests only
  python run_tests.py integration             # Run integration tests only
  python run_tests.py e2e                     # Run E2E tests only
  python run_tests.py all                     # Run all tests
  python run_tests.py coverage                # Generate coverage report
  python run_tests.py specific tests/unit/test_orchestrator_agent.py
  python run_tests.py markers unit integration
  python run_tests.py --verbose unit          # Run with verbose output
  python run_tests.py --no-cleanup integration # Skip cleanup before tests
        """
    )
    
    parser.add_argument(
        "test_type",
        choices=["unit", "integration", "e2e", "all", "coverage", "specific", "markers"],
        help="Type of tests to run"
    )
    
    parser.add_argument(
        "target",
        nargs="?",
        help="Specific test file/function (for 'specific') or markers (for 'markers')"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Run tests with verbose output"
    )
    
    parser.add_argument(
        "--no-cleanup",
        action="store_true",
        help="Skip cleanup before running tests"
    )
    
    parser.add_argument(
        "--markers",
        nargs="+",
        help="Test markers to run (for 'markers' command)"
    )
    
    args = parser.parse_args()
    
    cleanup_before = not args.no_cleanup
    
    try:
        if args.test_type == "unit":
            exit_code = run_unit_tests(verbose=args.verbose)
        elif args.test_type == "integration":
            exit_code = run_integration_tests(verbose=args.verbose, cleanup_before=cleanup_before)
        elif args.test_type == "e2e":
            exit_code = run_e2e_tests(verbose=args.verbose, cleanup_before=cleanup_before)
        elif args.test_type == "all":
            exit_code = run_all_tests(verbose=args.verbose, cleanup_before=cleanup_before)
        elif args.test_type == "coverage":
            exit_code = run_coverage_report()
        elif args.test_type == "specific":
            if not args.target:
                print("❌ Error: 'specific' requires a target test file/function")
                sys.exit(1)
            exit_code = run_specific_test(args.target, verbose=args.verbose)
        elif args.test_type == "markers":
            markers = args.markers or [args.target] if args.target else []
            if not markers:
                print("❌ Error: 'markers' requires marker names")
                sys.exit(1)
            exit_code = run_tests_with_markers(markers, verbose=args.verbose)
        else:
            print(f"❌ Error: Unknown test type: {args.test_type}")
            sys.exit(1)
        
        if exit_code == 0:
            print("\n🎉 All tests completed successfully!")
        else:
            print(f"\n❌ Tests failed with exit code: {exit_code}")
        
        sys.exit(exit_code)
        
    except KeyboardInterrupt:
        print("\n⚠️ Test execution interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
