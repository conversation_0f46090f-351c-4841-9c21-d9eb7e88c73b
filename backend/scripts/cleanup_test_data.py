#!/usr/bin/env python3
"""
Cleanup script for test data in MinIO, Qdrant, and GraphDB.
Run this before executing tests to ensure clean state.
"""
import asyncio
import logging
import sys
from pathlib import Path

# Add backend to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from core.dependencies import get_dependencies, cleanup_dependencies
from tests.utils.cleanup import DataCleanup

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def cleanup_minio_only():
    """Clean up only MinIO test data."""
    logger.info("Starting MinIO cleanup...")
    
    try:
        deps = await get_dependencies()
        cleanup_util = DataCleanup(
            deps.minio_client,
            deps.qdrant_client,
            deps.graphdb_client
        )
        
        success = await cleanup_util.minio_cleanup.cleanup_test_files()
        
        if success:
            logger.info("✅ MinIO cleanup completed successfully")
        else:
            logger.error("❌ MinIO cleanup failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ MinIO cleanup error: {e}")
        return False
    finally:
        await cleanup_dependencies()
    
    return True


async def cleanup_qdrant_only():
    """Clean up only Qdrant test data."""
    logger.info("Starting Qdrant cleanup...")
    
    try:
        deps = await get_dependencies()
        cleanup_util = DataCleanup(
            deps.minio_client,
            deps.qdrant_client,
            deps.graphdb_client
        )
        
        success = await cleanup_util.qdrant_cleanup.cleanup_test_collections()
        
        if success:
            logger.info("✅ Qdrant cleanup completed successfully")
        else:
            logger.error("❌ Qdrant cleanup failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Qdrant cleanup error: {e}")
        return False
    finally:
        await cleanup_dependencies()
    
    return True


async def cleanup_graphdb_only():
    """Clean up only GraphDB test data."""
    logger.info("Starting GraphDB cleanup...")
    
    try:
        deps = await get_dependencies()
        cleanup_util = DataCleanup(
            deps.minio_client,
            deps.qdrant_client,
            deps.graphdb_client
        )
        
        success = await cleanup_util.graphdb_cleanup.cleanup_test_repository()
        
        if success:
            logger.info("✅ GraphDB cleanup completed successfully")
        else:
            logger.error("❌ GraphDB cleanup failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ GraphDB cleanup error: {e}")
        return False
    finally:
        await cleanup_dependencies()
    
    return True


async def cleanup_all():
    """Clean up all test data from all systems."""
    logger.info("Starting comprehensive cleanup of all test data...")
    
    try:
        deps = await get_dependencies()
        cleanup_util = DataCleanup(
            deps.minio_client,
            deps.qdrant_client,
            deps.graphdb_client
        )
        
        success = await cleanup_util.cleanup_all()
        
        if success:
            logger.info("✅ All cleanup operations completed successfully")
        else:
            logger.error("❌ Some cleanup operations failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Cleanup error: {e}")
        return False
    finally:
        await cleanup_dependencies()
    
    return True


async def cleanup_by_prefix(prefix: str):
    """Clean up test data by prefix."""
    logger.info(f"Starting cleanup for prefix: {prefix}")
    
    try:
        deps = await get_dependencies()
        cleanup_util = DataCleanup(
            deps.minio_client,
            deps.qdrant_client,
            deps.graphdb_client
        )
        
        # Clean MinIO by prefix
        minio_success = await cleanup_util.minio_cleanup.cleanup_by_prefix(prefix)
        
        # Clean GraphDB by prefix
        graphdb_success = await cleanup_util.graphdb_cleanup.cleanup_by_prefix(prefix)
        
        success = minio_success and graphdb_success
        
        if success:
            logger.info(f"✅ Prefix cleanup completed successfully for: {prefix}")
        else:
            logger.error(f"❌ Prefix cleanup failed for: {prefix}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Prefix cleanup error: {e}")
        return False
    finally:
        await cleanup_dependencies()
    
    return True


def print_usage():
    """Print usage information."""
    print("""
Usage: python cleanup_test_data.py [command] [options]

Commands:
    all         Clean up all test data (default)
    minio       Clean up only MinIO test data
    qdrant      Clean up only Qdrant test data
    graphdb     Clean up only GraphDB test data
    prefix <p>  Clean up data with specific prefix

Examples:
    python cleanup_test_data.py
    python cleanup_test_data.py all
    python cleanup_test_data.py minio
    python cleanup_test_data.py qdrant
    python cleanup_test_data.py graphdb
    python cleanup_test_data.py prefix test_
""")


async def main():
    """Main entry point."""
    if len(sys.argv) < 2:
        command = "all"
    else:
        command = sys.argv[1].lower()
    
    if command in ["-h", "--help", "help"]:
        print_usage()
        return
    
    success = False
    
    if command == "all":
        success = await cleanup_all()
    elif command == "minio":
        success = await cleanup_minio_only()
    elif command == "qdrant":
        success = await cleanup_qdrant_only()
    elif command == "graphdb":
        success = await cleanup_graphdb_only()
    elif command == "prefix":
        if len(sys.argv) < 3:
            logger.error("Prefix command requires a prefix argument")
            print_usage()
            sys.exit(1)
        prefix = sys.argv[2]
        success = await cleanup_by_prefix(prefix)
    else:
        logger.error(f"Unknown command: {command}")
        print_usage()
        sys.exit(1)
    
    if not success:
        sys.exit(1)
    
    logger.info("🎉 Cleanup completed successfully!")


if __name__ == "__main__":
    asyncio.run(main())
