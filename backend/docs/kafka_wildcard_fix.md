# Kafka Wildcard Topic Subscription Fix

## Problem

The Kafka processor was failing to subscribe to wildcard topics with the error:
```
'usecases.*' is not a valid topic name
```

This occurred because:
1. The code was trying to use shell-style wildcards (`usecases.*`) as literal topic names
2. aiokafka doesn't support shell-style wildcards in the `subscribe()` method
3. aiokafka requires **regular expression patterns** using the `pattern` parameter

## Root Cause

The original implementation in `KafkaProcessor.start()` was:
```python
topics = [
    "usecases.*"  # This is NOT a valid topic name
]
await self.deps.kafka_client.subscribe_to_topics(topics)
```

And `KafkaClient.subscribe_to_topics()` was calling:
```python
self.consumer.subscribe(topics)  # This expects literal topic names
```

## Solution

### 1. Added Pattern Subscription Support

Added a new method to `KafkaClient`:
```python
async def subscribe_to_pattern(self, pattern: str):
    """Subscribe to Kafka topics using a regex pattern."""
    if not self.consumer:
        raise RuntimeError("Kafka consumer not initialized")
        
    self.consumer.subscribe(pattern=pattern)
    logger.info(f"Subscribed to topic pattern: {pattern}")
```

### 2. Updated KafkaProcessor to Use Configured Patterns

Modified `KafkaProcessor.start()` to:
1. Read the configured topic pattern from settings
2. Convert shell-style wildcards to regex patterns
3. Use the new pattern subscription method

```python
# Get the configured topic pattern from settings
usecase_pattern = self.deps.settings.kafka.usecase_topic_pattern

# Convert shell-style wildcard to regex pattern
# "data.usecases.*" becomes "^data\.usecases\..*$"
regex_pattern = f"^{usecase_pattern.replace('.', r'\.').replace('*', '.*')}$"

# Subscribe to usecases topics using regex pattern
await self.deps.kafka_client.subscribe_to_pattern(regex_pattern)
```

### 3. Pattern Conversion Examples

| Shell Pattern | Regex Pattern | Matches | Doesn't Match |
|---------------|---------------|---------|---------------|
| `usecases.*` | `^usecases\..*$` | `usecases.portfolio`, `usecases.building_data` | `other.usecases.test`, `usecases` |
| `data.usecases.*` | `^data\.usecases\..*$` | `data.usecases.portfolio`, `data.usecases.building.test` | `usecases.portfolio`, `data.other.portfolio` |

## Configuration

The topic pattern is configured in `config/settings.py` and `config/config.yaml`:

```yaml
kafka:
  usecase_topic_pattern: "data.usecases.*"
```

This allows for flexible configuration without code changes.

## Testing

Comprehensive tests were added in `tests/integration/test_kafka_wildcard_subscription.py`:

1. **Pattern Subscription Test**: Verifies `KafkaClient.subscribe_to_pattern()` works correctly
2. **Pattern Conversion Test**: Tests shell-to-regex conversion logic
3. **Message Handler Matching**: Ensures message handlers work with pattern-subscribed topics
4. **Processor Integration Test**: Verifies the complete flow works end-to-end
5. **Wildcard Matching Test**: Tests various topic names against regex patterns

## Benefits

1. **Proper Wildcard Support**: Now correctly subscribes to topics matching patterns
2. **Configuration-Driven**: Uses configured patterns instead of hardcoded values
3. **Flexible Patterns**: Supports any shell-style wildcard pattern
4. **Backward Compatible**: Existing functionality remains unchanged
5. **Well Tested**: Comprehensive test coverage for all scenarios

## Usage

The Kafka processor will now automatically:
1. Listen to all topics matching the configured pattern
2. Process messages from any topic under the pattern (e.g., `data.usecases.portfolio`, `data.usecases.building_data`, etc.)
3. Handle new topics that match the pattern without code changes

## Files Modified

- `backend/infrastructure/kafka_client.py`: Added `subscribe_to_pattern()` method
- `backend/services/kafka_processor.py`: Updated to use pattern subscription
- `backend/tests/integration/test_kafka_wildcard_subscription.py`: Added comprehensive tests

## Verification

Run the tests to verify the fix:
```bash
cd backend
uv run python -m pytest tests/integration/test_kafka_wildcard_subscription.py -v
```

All tests should pass, confirming that wildcard topic subscription now works correctly.
