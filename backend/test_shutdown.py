#!/usr/bin/env python3
"""
Test script to verify Kafka processor shutdown improvements.
This script starts the Kafka processor and measures shutdown time.
"""
import asyncio
import time
import signal
import sys
import logging
from pathlib import Path

# Add backend to path
sys.path.insert(0, str(Path(__file__).parent))

from core.dependencies import get_dependencies, cleanup_dependencies
from services.kafka_processor import KafkaProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_shutdown_performance():
    """Test the shutdown performance of the Kafka processor."""
    processor = None
    shutdown_event = asyncio.Event()
    shutdown_start_time = None
    
    def signal_handler(signum, frame):
        nonlocal shutdown_start_time
        logger.info(f"Received signal {signum}, starting shutdown timer...")
        shutdown_start_time = time.time()
        shutdown_event.set()

    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        logger.info("Initializing dependencies...")
        deps = await get_dependencies()
        
        logger.info("Creating Kafka processor...")
        processor = KafkaProcessor(deps)
        
        logger.info("Starting Kafka processor...")
        logger.info("Press Ctrl+C to test shutdown performance")
        
        # Start processing in a task
        processor_task = asyncio.create_task(processor.start())
        
        # Wait for shutdown signal or processor completion
        done, pending = await asyncio.wait(
            [processor_task, asyncio.create_task(shutdown_event.wait())],
            return_when=asyncio.FIRST_COMPLETED
        )
        
        # Cancel any pending tasks
        for task in pending:
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass

    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
        if shutdown_start_time is None:
            shutdown_start_time = time.time()
    except Exception as e:
        logger.error(f"Error in Kafka processor: {e}")
    finally:
        if processor:
            logger.info("Stopping Kafka processor...")
            processor_stop_start = time.time()
            await processor.stop()
            processor_stop_time = time.time() - processor_stop_start
            logger.info(f"Kafka processor stopped in {processor_stop_time:.2f}s")
        
        logger.info("Cleaning up dependencies...")
        cleanup_start = time.time()
        await cleanup_dependencies()
        cleanup_time = time.time() - cleanup_start
        logger.info(f"Dependencies cleaned up in {cleanup_time:.2f}s")
        
        if shutdown_start_time:
            total_shutdown_time = time.time() - shutdown_start_time
            logger.info(f"🎉 Total shutdown time: {total_shutdown_time:.2f}s")
        
        logger.info("Shutdown complete")


if __name__ == "__main__":
    try:
        asyncio.run(test_shutdown_performance())
    except KeyboardInterrupt:
        logger.info("Test interrupted")
    except Exception as e:
        logger.error(f"Test failed: {e}")
        sys.exit(1)
