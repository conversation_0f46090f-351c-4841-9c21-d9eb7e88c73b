"""
Dependency injection system for the RDF Agent backend.
Provides centralized configuration and service management.
"""
import logging
from dataclasses import dataclass
from typing import Optional
import asyncio
from contextlib import asynccontextmanager

from config.settings import Settings
from infrastructure.kafka_client import KafkaClient
from infrastructure.graphdb_client_new import GraphD<PERSON>lient
from infrastructure.minio_client import MinIOClient
from infrastructure.qdrant_client import QdrantClient
from infrastructure.database import DatabaseManager
from services.docling_service import DoclingService
from services.ttl_converter import TTLConverter
from services.session_manager import SessionManager


logger = logging.getLogger(__name__)


@dataclass
class Dependencies:
    """Central dependency container for the application."""
    settings: Settings
    kafka_client: Optional[KafkaClient] = None
    graphdb_client: Optional[GraphDBClient] = None
    minio_client: Optional[MinIOClient] = None
    qdrant_client: Optional[QdrantClient] = None
    database_manager: Optional[DatabaseManager] = None
    docling_service: Optional[DoclingService] = None
    ttl_converter: Optional[TTLConverter] = None
    session_manager: Optional[SessionManager] = None

    async def initialize(self):
        """Initialize all services and clients."""
        # Initialize database first
        self.database_manager = DatabaseManager(self.settings.database.url)
        await self.database_manager.initialize()
        
        # Initialize external service clients
        self.kafka_client = KafkaClient(self.settings.kafka)
        self.graphdb_client = GraphDBClient(self.settings.graphdb)
        self.minio_client = MinIOClient(self.settings.minio)
        self.qdrant_client = QdrantClient(self.settings.qdrant)

        # Initialize services
        self.docling_service = DoclingService(self.settings.docling)
        self.ttl_converter = TTLConverter()
        self.session_manager = SessionManager(self.database_manager)

        # Initialize clients (with error handling for optional services)
        await self.kafka_client.initialize()
        await self.graphdb_client.initialize()

        try:
            await self.minio_client.initialize()
        except Exception as e:
            logger.warning(f"MinIO initialization failed: {e}")
            self.minio_client = None

        try:
            await self.qdrant_client.initialize()
        except Exception as e:
            logger.warning(f"Qdrant initialization failed: {e}")
            self.qdrant_client = None

    async def cleanup(self):
        """Clean up all resources with parallel execution and timeouts."""
        logger.info("Cleaning up dependencies...")

        # Create cleanup tasks for all clients
        cleanup_tasks = []

        if self.kafka_client:
            cleanup_tasks.append(self._safe_cleanup("Kafka", self.kafka_client.close(), 15.0))

        if self.graphdb_client:
            cleanup_tasks.append(self._safe_cleanup("GraphDB", self.graphdb_client.close(), 10.0))

        if self.minio_client:
            cleanup_tasks.append(self._safe_cleanup("MinIO", self.minio_client.close(), 5.0))

        if self.qdrant_client:
            cleanup_tasks.append(self._safe_cleanup("Qdrant", self.qdrant_client.close(), 5.0))

        if self.database_manager:
            cleanup_tasks.append(self._safe_cleanup("Database", self.database_manager.close(), 5.0))

        # Run all cleanup tasks in parallel
        if cleanup_tasks:
            await asyncio.gather(*cleanup_tasks, return_exceptions=True)

        logger.info("Dependencies cleanup completed")

    async def _safe_cleanup(self, service_name: str, cleanup_coro, timeout: float):
        """Safely cleanup a service with timeout and error handling."""
        try:
            await asyncio.wait_for(cleanup_coro, timeout=timeout)
            logger.info(f"{service_name} client closed successfully")
        except asyncio.TimeoutError:
            logger.warning(f"{service_name} client cleanup timed out after {timeout}s")
        except Exception as e:
            logger.warning(f"Error closing {service_name} client: {e}")


# Global dependencies instance
_dependencies: Optional[Dependencies] = None


async def get_dependencies() -> Dependencies:
    """Get the global dependencies instance."""
    global _dependencies
    if _dependencies is None:
        settings = Settings()
        _dependencies = Dependencies(settings=settings)
        await _dependencies.initialize()
    return _dependencies


@asynccontextmanager
async def lifespan_context():
    """Context manager for application lifespan."""
    deps = await get_dependencies()
    try:
        yield deps
    finally:
        await deps.cleanup()


async def cleanup_dependencies():
    """Clean up global dependencies."""
    global _dependencies
    if _dependencies:
        await _dependencies.cleanup()
        _dependencies = None
