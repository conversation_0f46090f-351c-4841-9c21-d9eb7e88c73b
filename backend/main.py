"""
Main application entry point for the RDF Agent backend.
Can run as API server or Kafka processor.
"""
import asyncio
import logging
import signal
import sys
from typing import Optional

import typer
import uvicorn

from core.dependencies import get_dependencies, cleanup_dependencies
from services.kafka_processor import KafkaProcessor


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = typer.Typer()


@app.command()
def api(
    host: str = "0.0.0.0",
    port: int = 8000,
    reload: bool = False,
    log_level: str = "info"
):
    """Run the FastAPI server."""
    logger.info(f"Starting API server on {host}:{port}")

    uvicorn.run(
        "api.main:app",
        host=host,
        port=port,
        reload=reload,
        log_level=log_level
    )


@app.command()
def kafka_processor():
    """Run the Kafka message processor."""
    logger.info("Starting Kafka processor...")

    async def run_processor():
        processor = None
        shutdown_event = asyncio.Event()

        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating graceful shutdown...")
            shutdown_event.set()

        # Set up signal handlers
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        try:
            # Get dependencies
            deps = await get_dependencies()

            # Create and start processor
            processor = KafkaProcessor(deps)

            # Start processing in a task
            processor_task = asyncio.create_task(processor.start())

            # Wait for shutdown signal or processor completion
            done, pending = await asyncio.wait(
                [processor_task, asyncio.create_task(shutdown_event.wait())],
                return_when=asyncio.FIRST_COMPLETED
            )

            # Cancel any pending tasks
            for task in pending:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass

        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt, shutting down...")
        except Exception as e:
            logger.error(f"Error in Kafka processor: {e}")
        finally:
            if processor:
                logger.info("Stopping Kafka processor...")
                await processor.stop()
            logger.info("Cleaning up dependencies...")
            await cleanup_dependencies()
            logger.info("Shutdown complete")

    # Run the async function
    asyncio.run(run_processor())


@app.command()
def test_connections():
    """Test connections to all external services."""

    async def test_all():
        try:
            deps = await get_dependencies()

            logger.info("Testing service connections...")

            # Test GraphDB
            try:
                graphdb_healthy = await deps.graphdb_client.health_check()
                logger.info(f"GraphDB: {'✓' if graphdb_healthy else '✗'}")
            except Exception as e:
                logger.error(f"GraphDB: ✗ - {e}")

            # Test Qdrant
            try:
                count = await deps.qdrant_client.count_points()
                logger.info(f"Qdrant: ✓ - {count} points")
            except Exception as e:
                logger.error(f"Qdrant: ✗ - {e}")

            # Test MinIO
            try:
                objects = await deps.minio_client.list_objects()
                logger.info(f"MinIO: ✓ - {len(objects)} objects")
            except Exception as e:
                logger.error(f"MinIO: ✗ - {e}")

            # Test Kafka
            try:
                topics = await deps.kafka_client.get_topics()
                logger.info(f"Kafka: ✓ - {len(topics)} topics")
            except Exception as e:
                logger.error(f"Kafka: ✗ - {e}")

            # Test Docling
            try:
                docling_healthy = await deps.docling_service.health_check()
                logger.info(f"Docling: {'✓' if docling_healthy else '✗'}")
            except Exception as e:
                logger.error(f"Docling: ✗ - {e}")

            logger.info("Connection tests completed")

        except Exception as e:
            logger.error(f"Error testing connections: {e}")
        finally:
            await cleanup_dependencies()

    asyncio.run(test_all())


@app.command()
def init_db():
    """Initialize the database with required tables."""

    async def initialize():
        try:
            deps = await get_dependencies()
            logger.info("Database initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing database: {e}")
        finally:
            await cleanup_dependencies()

    asyncio.run(initialize())


@app.command()
def process_sample_data():
    """Process sample data from the context files."""

    async def process_data():
        try:
            deps = await get_dependencies()

            # Read sample data
            import json
            from pathlib import Path

            sample_file = Path("../.context/example_files/example_input.json")
            if not sample_file.exists():
                logger.error("Sample data file not found")
                return

            with open(sample_file, 'r') as f:
                sample_data = json.load(f)

            logger.info(f"Processing {len(sample_data)} sample items...")

            # Create Kafka processor
            processor = KafkaProcessor(deps)

            # Create a sample usecase message for testing
            sample_usecase_message = {
                "graphTemplate": "@prefix ex: <http://example.org/> .\\nNode1 hasRelation Node2 .",
                "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .\\n@prefix ex: <http://example.org/> .\\nex:SampleBuilding rdf:type ex:Building .",
                "accessRights": None,
                "useCase": "SampleProcessing",
                "graphMetadata": [
                    {
                        "id": "sample-building-001",
                        "classType": "https://ibpdi.datacat.org/class/Building",
                        "propertiesValues": {
                            "Name": "Sample Building",
                            "BuildingCode": "SB001",
                            "EnergyEfficiencyClass": "A"
                        }
                    }
                ]
            }

            logger.info("Processing sample usecase message...")
            await processor._handle_usecase_message("usecases.sample_processing", sample_usecase_message)

            logger.info("Sample data processing completed")

        except Exception as e:
            logger.error(f"Error processing sample data: {e}")
        finally:
            await cleanup_dependencies()

    asyncio.run(process_data())


@app.command()
def query(
    text: str,
    query_type: str = "natural_language",
    session_id: Optional[str] = None
):
    """Execute a query using the orchestrator agent."""

    async def run_query():
        try:
            deps = await get_dependencies()

            from agents.orchestrator_agent import OrchestratorAgent
            from models.data_models import QueryRequest
            import uuid

            # Create query request
            query_request = QueryRequest(
                query_id=str(uuid.uuid4()),
                session_id=session_id,
                query_text=text,
                query_type=query_type
            )

            # Create orchestrator
            orchestrator = OrchestratorAgent(deps)

            # Process query
            response = await orchestrator.process_query(query_request)

            # Print response
            print(f"\nQuery: {text}")
            print(f"Response: {response.response_text}")
            if response.sparql_query:
                print(f"SPARQL: {response.sparql_query}")
            if response.results:
                print(f"Results: {len(response.results)} items")
            print(f"Processing time: {response.processing_time:.2f}s")

        except Exception as e:
            logger.error(f"Error executing query: {e}")
        finally:
            await cleanup_dependencies()

    asyncio.run(run_query())


if __name__ == "__main__":
    app()
