This is a python project for an agentic ai answering questions about RDF databases.
Concept is as follows: User chooses a use-case (basically an RDF database that has the database that the user wants to ask a question about, you can think of this as a knowledge base)
the agents create SPARQL queries to try to answer the user's questions and use LLM to generate a user friendly answer. Some nodes have paths to documents in them so in that case the agent may query a vector store to get some additional context for the answer.

For all the AI Agents logic, pydantic ai is used. pydantic provides a dependency injection system for the agents. So an agent is defined once and then it receives dependencies via the pydantic dependency injection system. An agent may have multiple tools (GraphDB queries, RAG from documents, etc.) and these tools can also get dependencies. The tools may also be agents themselves. Each agent should have a clear and simple purpose.

The usecases are added and synced over kafka topics.
assume it is data.usecases.\* where the star represents the name of the usecase.
it may need to check if it has all the data of the usecase (smartly and not ressource intensive) otherwise it needs to get all the past messages in that topic in case it was not processed yet.
The type of topics would be either create or update (for a single node in the RDF data). If the coming node is of type BuildingCertificate it means that it has a path to a document in it. This exists within a MinIO object store. In this case it needs to be embedded and added to the Qdrant vector store with enough metadata to be able to query it and filter it.
Other than the kafka, the user may add usecases over api, but this will use kafka to get the data as well.
It sotres the RDF in a tripple store database (in our case GraphDB, but it should not matter). It uses the rdflib library to interact with it.

The code should provide a REST API to query the agents.
This includes starting a session, and storing the conversation history in a json field. So one table, session, metadata, message_history.
It should use sqlite, with sqlalchemy and fastapi. No migrations logic needed.

A conversation is started by providing a usecase. so an api to list usecases is needed.
A conversation is strictly about one usecase.

a usecase has an rdf store (called repository in graphdb), a vector store collection, and a minio folder within a bucket called "building_certificates".

The project should be heavily unit and integration tested.

Stack:

- FastAPI
- pydantic
- pydantic-ai
- SQLAlchemy
- SQLite
- Kafka
- MinIO
- Qdrant
- PydanticAI
- rdflib
- pytest
