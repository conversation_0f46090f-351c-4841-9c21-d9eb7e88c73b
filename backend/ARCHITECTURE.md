# Agentic AI Architecture for RDF Database Querying

## Overview

This architecture implements an agentic AI system that answers questions about RDF databases using pydantic-ai for agent logic, FastAPI for the REST API, and integrates with Kafka for use-case synchronization, GraphDB for RDF storage, MinIO for document storage, and Qdrant for vector search.

The system follows a clean, folder-based structure with dependency injection at its core, allowing for easy testing and maintainability.

## System Architecture

```mermaid
flowchart TD
    A[User] -->|HTTP Requests| B[FastAPI REST API]
    B --> C[Session Manager]
    C --> D[UseCase Registry]
    D --> E[Kafka Consumer]
    E --> F[GraphDB RDF Store]
    E --> G[MinIO Document Store]
    G --> H[Docling Document Extractor]
    H --> I[Qdrant Vector Store]
    B --> J[AI Agent Orchestrator]
    J --> K[RDF Query Agent]
    J --> L[RAG Agent]
    K --> F
    L --> I
    J --> M[Response Generator]
    M --> B
```

## Configuration Strategy

- **Environment Variables**: All sensitive keys and connection strings stored in `.env`
- **YAML Configuration**: Application configuration in `config.yaml` with environment-specific overrides
- **Pydantic Settings**: Type-safe configuration loading using `pydantic-settings`

## Folder Structure

```
backend/
├── config/                 # Configuration files and settings
├── core/                   # Core application logic and dependency injection
├── models/                 # Database models and data schemas
├── agents/                 # AI agents and tools
├── services/               # Business logic services
├── api/                    # FastAPI routes and endpoints
├── infrastructure/         # External service integrations
├── utils/                  # Utility functions and helpers
└── tests/                  # Unit and integration tests
```

## Detailed Component Specifications

### 1. Configuration Layer (`config/`)

#### `config/settings.py`

```python
"""
Central configuration management using pydantic-settings.
Loads configuration from config.yaml and .env files.
Provides type-safe access to all application settings.
"""
from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import Field
from typing import Dict, Any

class KafkaSettings(BaseSettings):
    bootstrap_servers: str = Field(default="localhost:9092")
    group_id: str = Field(default="rdf-agent-consumer")
    usecase_topic_pattern: str = Field(default="data.usecases.*")

class GraphDBSettings(BaseSettings):
    url: str = Field(default="http://localhost:7200")
    default_repository: str = Field(default="default")

class MinIOSettings(BaseSettings):
    endpoint: str = Field(default="localhost:9000")
    access_key: str = Field(default="minioadmin")
    secret_key: str = Field(default="minioadmin")
    bucket_name: str = Field(default="building_certificates")

class QdrantSettings(BaseSettings):
    host: str = Field(default="localhost")
    port: int = Field(default=6333)
    collection_name: str = Field(default="building_certificates")

class DoclingSettings(BaseSettings):
    """Configuration for Docling document extraction."""
    enabled: bool = Field(default=True)
    output_format: str = Field(default="markdown")  # Can be "markdown", "json", "text"
    timeout: int = Field(default=300)  # Timeout in seconds for document processing

class DatabaseSettings(BaseSettings):
    url: str = Field(default="sqlite:///./app.db")

class AISettings(BaseSettings):
    model: str = Field(default="openai:gpt-4.1-mini")
    api_key: str = Field(default="")

class Settings(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        env_nested_delimiter="__"
    )

    kafka: KafkaSettings = KafkaSettings()
    graphdb: GraphDBSettings = GraphDBSettings()
    minio: MinIOSettings = MinIOSettings()
    qdrant: QdrantSettings = QdrantSettings()
    docling: DoclingSettings = DoclingSettings()
    database: DatabaseSettings = DatabaseSettings()
    ai: AISettings = AISettings()
```

#### `config/config.yaml`

```yaml
# Application configuration
app:
  name: "RDF Agent System"
  version: "0.1.0"
  debug: false

# Kafka configuration
kafka:
  bootstrap_servers: "kafka:9092"
  group_id: "rdf-agent-consumer"
  usecase_topic_pattern: "data.usecases.*"

# GraphDB configuration
graphdb:
  url: "http://graphdb:7200"
  default_repository: "rdf_agent_db"

# MinIO configuration
minio:
  endpoint: "minio:9000"
  bucket_name: "building_certificates"

# Qdrant configuration
qdrant:
  host: "qdrant"
  port: 6333

# Docling configuration
docling:
  enabled: true
  output_format: "markdown"
  timeout: 300

# Database configuration
database:
  url: "sqlite:///./app.db"

# AI configuration
ai:
  model: "openai:gpt-4.1-mini"
```

### 2. Core Layer (`core/`)

#### `core/dependencies.py`

```python
"""
Dependency injection container for the entire application.
Defines the main dependency class that will be injected into all agents and services.
"""
from dataclasses import dataclass
from typing import Dict, Any
from ..infrastructure import (
    GraphDBClient,
    KafkaConsumer,
    MinIOClient,
    QdrantClient,
    DoclingClient,
    DatabaseSession
)
from ..config import Settings

@dataclass
class AppDependencies:
    """Main dependency container for the application."""
    settings: Settings
    graphdb_client: GraphDBClient
    kafka_consumer: KafkaConsumer
    minio_client: MinIOClient
    qdrant_client: QdrantClient
    docling_client: DoclingClient
    db_session: DatabaseSession

    async def get_usecase_repository(self, usecase_name: str) -> str:
        """Get the GraphDB repository name for a specific usecase."""
        # Implementation to map usecase to repository
        pass

    async def get_usecase_collection(self, usecase_name: str) -> str:
        """Get the Qdrant collection name for a specific usecase."""
        # Implementation to map usecase to collection
        pass
```

### 3. Models Layer (`models/`)

#### `models/database.py`

```python
"""
SQLAlchemy models for the application database.
Uses SQLite with SQLAlchemy ORM.
"""
from sqlalchemy import Column, String, Text, DateTime, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
import json

Base = declarative_base()

class Session(Base):
    """Represents a user conversation session."""
    __tablename__ = "sessions"

    id = Column(String, primary_key=True)
    usecase_name = Column(String, nullable=False)
    user_id = Column(String, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    message_history = Column(Text, default="[]")  # JSON string of messages

    @property
    def messages(self) -> List[Dict[str, Any]]:
        """Parse message history from JSON."""
        return json.loads(self.message_history) if self.message_history else []

    @messages.setter
    def messages(self, value: List[Dict[str, Any]]):
        """Set message history as JSON."""
        self.message_history = json.dumps(value)
```

#### `models/rdf.py`

```python
"""
Pydantic models for RDF data structures.
Defines the data schema for usecase data from Kafka.
"""
from pydantic import BaseModel, Field
from typing import List, Dict, Optional, Any

class GraphMetadataItem(BaseModel):
    """Represents a single node in the RDF graph metadata."""
    id: str
    class_type: str = Field(alias="classType")
    properties_values: Dict[str, Any] = Field(alias="propertiesValues")

class UseCaseData(BaseModel):
    """Represents a complete usecase from Kafka."""
    graph_template: str = Field(alias="graphTemplate")
    graph_data: str = Field(alias="graphData")
    access_rights: Optional[Any] = Field(alias="accessRights")
    use_case: Optional[str] = Field(alias="useCase")
    graph_metadata: List[GraphMetadataItem] = Field(alias="graphMetadata")

    class Config:
        populate_by_name = True
```

### 4. Agents Layer (`agents/`)

#### `agents/base.py`

```python
"""
Base agent class and common utilities for all agents.
"""
from pydantic_ai import Agent, RunContext
from typing import TypeVar, Generic
from ..core import AppDependencies

T = TypeVar('T')

class BaseAgent(Generic[T]):
    """Base class for all AI agents in the system."""

    def __init__(self, model: str, deps_type: type):
        self.agent = Agent(model, deps_type=deps_type)

    async def run(self, prompt: str, deps: AppDependencies) -> T:
        """Run the agent with the given prompt and dependencies."""
        return await self.agent.run(prompt, deps=deps)
```

#### `agents/rdf_query_agent.py`

```python
"""
Agent responsible for generating and executing SPARQL queries against GraphDB.
"""
from pydantic_ai import Agent, RunContext, Tool
from typing import List, Dict, Any
from rdflib import Graph
from ..core import AppDependencies
from ..infrastructure import GraphDBClient
from ..models import UseCaseData

class RDFQueryAgent:
    """Agent that creates and executes SPARQL queries against RDF data."""

    def __init__(self, settings):
        self.agent = Agent(
            settings.ai.model,
            deps_type=AppDependencies,
            system_prompt="You are an expert at creating SPARQL queries for RDF databases. Always return valid SPARQL queries."
        )

    @self.agent.tool
    async def execute_sparql_query(self, ctx: RunContext[AppDependencies], query: str, usecase_name: str) -> List[Dict[str, Any]]:
        """Execute a SPARQL query against the GraphDB repository for the given usecase."""
        repository = await ctx.deps.get_usecase_repository(usecase_name)
        return await ctx.deps.graphdb_client.execute_query(repository, query)

    @self.agent.tool
    async def get_rdf_schema(self, ctx: RunContext[AppDependencies], usecase_name: str) -> str:
        """Get the RDF schema/ontology for the given usecase."""
        repository = await ctx.deps.get_usecase_repository(usecase_name)
        return await ctx.deps.graphdb_client.get_schema(repository)
```

#### `agents/rag_agent.py`

```python
"""
Agent responsible for Retrieval-Augmented Generation using Qdrant vector store.
"""
from pydantic_ai import Agent, RunContext
from typing import List, Dict, Any
from ..core import AppDependencies
from ..infrastructure import QdrantClient

class RAGAgent:
    """Agent that performs semantic search and retrieval from vector store."""

    def __init__(self, settings):
        self.agent = Agent(
            settings.ai.model,
            deps_type=AppDependencies,
            system_prompt="You are an expert at retrieving relevant information from documents using semantic search."
        )

    @self.agent.tool
    async def semantic_search(self, ctx: RunContext[AppDependencies], query: str, usecase_name: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Perform semantic search in the Qdrant collection for the given usecase."""
        collection = await ctx.deps.get_usecase_collection(usecase_name)
        return await ctx.deps.qdrant_client.search(collection, query, limit=limit)

    @self.agent.tool
    async def get_document_context(self, ctx: RunContext[AppDependencies], document_id: str, usecase_name: str) -> str:
        """Retrieve the full document content from the vector store for the given document ID."""
        # Implementation to fetch document content from Qdrant
        pass
```

#### `agents/orchestrator_agent.py`

```python
"""
Main orchestrator agent that coordinates between RDF query agent and RAG agent.
"""
from pydantic_ai import Agent, RunContext
from typing import Dict, Any, List
from ..core import AppDependencies
from ..models import UseCaseData

class OrchestratorAgent:
    """Main agent that orchestrates the conversation and delegates to specialized agents."""

    def __init__(self, settings, rdf_agent, rag_agent):
        self.agent = Agent(
            settings.ai.model,
            deps_type=AppDependencies,
            system_prompt="You are a helpful assistant that answers questions about buildings and addresses using RDF data and document retrieval."
        )
        self.rdf_agent = rdf_agent
        self.rag_agent = rag_agent

    @self.agent.tool
    async def delegate_to_rdf_agent(self, ctx: RunContext[AppDependencies], question: str, usecase_name: str) -> Dict[str, Any]:
        """Delegate question to RDF query agent."""
        return await self.rdf_agent.run(question, ctx.deps)

    @self.agent.tool
    async def delegate_to_rag_agent(self, ctx: RunContext[AppDependencies], question: str, usecase_name: str) -> Dict[str, Any]:
        """Delegate question to RAG agent."""
        return await self.rag_agent.run(question, ctx.deps)

    @self.agent.tool
    async def check_building_certificate(self, ctx: RunContext[AppDependencies], building_id: str, usecase_name: str) -> bool:
        """Check if a building has a certificate document that needs RAG processing."""
        # Check if building node has document path in metadata
        # This would query the GraphDB or use cached metadata
        pass
```

### 5. Services Layer (`services/`)

#### `services/usecase_service.py`

```python
"""
Service for managing usecases from Kafka topics.
Handles synchronization and data processing.
"""
from typing import List, Dict, Any
from ..models import UseCaseData
from ..core import AppDependencies
from ..infrastructure import GraphDBClient, MinIOClient, QdrantClient, DoclingClient

class UseCaseService:
    """Manages usecase data from Kafka and synchronizes with external services."""

    def __init__(self, deps: AppDependencies):
        self.deps = deps

    async def process_usecase_message(self, message: Dict[str, Any], topic: str) -> None:
        """Process a Kafka message containing usecase data."""
        usecase_data = UseCaseData(**message)
        usecase_name = self.extract_usecase_name(topic)

        # Store RDF data in GraphDB
        await self.store_rdf_data(usecase_name, usecase_data)

        # Check for BuildingCertificate nodes and process documents
        await self.process_building_certificates(usecase_name, usecase_data)

    async def store_rdf_data(self, usecase_name: str, data: UseCaseData) -> None:
        """Store RDF data in GraphDB repository."""
        repository = await self.deps.get_usecase_repository(usecase_name)
        await self.deps.graphdb_client.insert_data(repository, data.graph_data)

    async def process_building_certificates(self, usecase_name: str, data: UseCaseData) -> None:
        """Process BuildingCertificate nodes and extract/embed documents."""
        for metadata_item in data.graph_metadata:
            if "BuildingCertificate" in metadata_item.class_type:
                # Extract document URL from properties
                document_url = metadata_item.properties_values.get("documentUrl")
                if document_url:
                    await self.process_document(usecase_name, document_url, metadata_item)

    async def process_document(self, usecase_name: str, document_url: str, metadata: Dict[str, Any]) -> None:
        """Extract document content using Docling, embed it, and store in Qdrant."""
        # Extract document content using Docling
        if self.deps.settings.docling.enabled:
            document_content = await self.deps.docling_client.extract_document(document_url)
        else:
            # Fallback: get raw content from MinIO if Docling is disabled
            document_content = await self.deps.minio_client.get_object_from_url(document_url)

        # Embed and store in Qdrant
        collection = await self.deps.get_usecase_collection(usecase_name)
        await self.deps.qdrant_client.upsert_document(
            collection=collection,
            document=document_content,
            metadata=metadata,
            document_id=document_url
        )
```

### 6. API Layer (`api/`)

#### `api/main.py`

```python
"""
Main FastAPI application entry point.
"""
from fastapi import FastAPI, Depends
from .dependencies import get_app_deps
from . import routes

def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    app = FastAPI(
        title="RDF Agent API",
        description="API for querying RDF databases with AI agents",
        version="0.1.0"
    )

    app.include_router(routes.router)
    return app

app = create_app()
```

#### `api/routes.py`

```python
"""
FastAPI route definitions.
"""
from fastapi import APIRouter, Depends, HTTPException
from typing import List
from ..core import AppDependencies, SessionManager
from ..models import Session as SessionModel
from .dependencies import get_app_deps, get_session_manager

router = APIRouter()

@router.get("/usecases")
async def list_usecases(
    session_manager: SessionManager = Depends(get_session_manager)
) -> List[str]:
    """List all available usecases."""
    return await session_manager.list_available_usecases()

@router.post("/sessions")
async def create_session(
    usecase_name: str,
    user_id: str,
    session_manager: SessionManager = Depends(get_session_manager)
) -> dict:
    """Create a new conversation session."""
    session_id = await session_manager.create_session(usecase_name, user_id)
    return {"session_id": session_id, "usecase": usecase_name}

@router.get("/sessions/{session_id}")
async def get_session(
    session_id: str,
    session_manager: SessionManager = Depends(get_session_manager)
) -> SessionModel:
    """Get session details."""
    return await session_manager.get_session(session_id)

@router.post("/sessions/{session_id}/query")
async def query_session(
    session_id: str,
    question: str,
    deps: AppDependencies = Depends(get_app_deps),
    session_manager: SessionManager = Depends(get_session_manager)
) -> dict:
    """Query the AI agent with a question in the context of a session."""
    session = await session_manager.get_session(session_id)

    # Run orchestrator agent
    orchestrator = deps.orchestrator_agent
    result = await orchestrator.run(question, deps)

    # Update session history
    await session_manager.update_session_history(
        session_id,
        {"role": "user", "content": question}
    )
    await session_manager.update_session_history(
        session_id,
        {"role": "assistant", "content": result.output}
    )

    return {"answer": result.output}
```

#### `api/dependencies.py`

```python
"""
FastAPI dependency injection functions.
"""
from fastapi import Depends
from ..core import AppDependencies, SessionManager
from ..config import Settings
from ..infrastructure import (
    GraphDBClient, KafkaConsumer, MinIOClient,
    QdrantClient, DoclingClient, DatabaseSession
)
from ..agents import RDFQueryAgent, RAGAgent, OrchestratorAgent

def get_settings() -> Settings:
    """Get application settings."""
    return Settings()

def get_app_deps(settings: Settings = Depends(get_settings)) -> AppDependencies:
    """Get the main application dependencies."""
    return AppDependencies(
        settings=settings,
        graphdb_client=GraphDBClient(settings.graphdb),
        kafka_consumer=KafkaConsumer(settings.kafka),
        minio_client=MinIOClient(settings.minio),
        qdrant_client=QdrantClient(settings.qdrant),
        docling_client=DoclingClient(settings.docling),
        db_session=DatabaseSession(settings.database)
    )

def get_session_manager(deps: AppDependencies = Depends(get_app_deps)) -> SessionManager:
    """Get session manager."""
    return SessionManager(deps.db_session)
```

### 7. Infrastructure Layer (`infrastructure/`)

#### `infrastructure/graphdb.py`

```python
"""
GraphDB client implementation using rdflib.
"""
from rdflib import Graph, URIRef, Literal
from rdflib.namespace import RDF, RDFS
from typing import List, Dict, Any
from ..config import GraphDBSettings

class GraphDBClient:
    """Client for interacting with GraphDB using rdflib."""

    def __init__(self, settings: GraphDBSettings):
        self.settings = settings

    async def execute_query(self, repository: str, query: str) -> List[Dict[str, Any]]:
        """Execute a SPARQL query against the repository."""
        # Implementation using rdflib or GraphDB HTTP API
        pass

    async def insert_data(self, repository: str, rdf_data: str) -> None:
        """Insert RDF data into the repository."""
        pass

    async def get_schema(self, repository: str) -> str:
        """Get the RDF schema for the repository."""
        pass
```

#### `infrastructure/kafka.py`

```python
"""
Kafka consumer implementation.
"""
from aiokafka import AIOKafkaConsumer
from typing import Callable, Dict, Any
from ..config import KafkaSettings

class KafkaConsumer:
    """Kafka consumer for usecase topics."""

    def __init__(self, settings: KafkaSettings):
        self.settings = settings
        self.consumer = None

    async def start(self) -> None:
        """Start the Kafka consumer."""
        self.consumer = AIOKafkaConsumer(
            bootstrap_servers=self.settings.bootstrap_servers,
            group_id=self.settings.group_id
        )
        await self.consumer.start()

    async def consume_messages(self, message_handler: Callable[[Dict[str, Any], str], None]) -> None:
        """Consume messages and pass to handler."""
        async for msg in self.consumer:
            topic = msg.topic
            message = msg.value.decode('utf-8')
            await message_handler(message, topic)

    async def stop(self) -> None:
        """Stop the Kafka consumer."""
        if self.consumer:
            await self.consumer.stop()
```

#### `infrastructure/minio.py`

```python
"""
MinIO client implementation.
"""
from minio import Minio
from minio.error import S3Error
from urllib.parse import urlparse
from ..config import MinIOSettings

class MinIOClient:
    """Client for interacting with MinIO object storage."""

    def __init__(self, settings: MinIOSettings):
        self.settings = settings
        self.client = Minio(
            settings.endpoint,
            access_key=settings.access_key,
            secret_key=settings.secret_key,
            secure=False
        )

    async def get_object(self, object_name: str) -> str:
        """Get an object from MinIO bucket."""
        try:
            response = self.client.get_object(self.settings.bucket_name, object_name)
            return response.read().decode('utf-8')
        except S3Error as e:
            raise Exception(f"MinIO error: {e}")

    async def get_object_from_url(self, url: str) -> str:
        """Get an object from MinIO using a URL."""
        # Parse URL to extract bucket and object name
        parsed = urlparse(url)
        object_name = parsed.path.lstrip('/')
        return await self.get_object(object_name)
```

#### `infrastructure/docling.py`

```python
"""
Docling document extraction client.
"""
from docling.document_converter import DocumentConverter
from typing import Optional
import asyncio
from ..config import DoclingSettings

class DoclingClient:
    """Client for extracting document content using Docling."""

    def __init__(self, settings: DoclingSettings):
        self.settings = settings
        self.converter = DocumentConverter()

    async def extract_document(self, source: str) -> str:
        """Extract document content and convert to configured output format."""
        # Run Docling conversion in a thread pool since it's synchronous
        loop = asyncio.get_event_loop()

        def _convert_document():
            result = self.converter.convert(source)
            if self.settings.output_format == "markdown":
                return result.document.export_to_markdown()
            elif self.settings.output_format == "json":
                return result.document.export_to_dict()
            elif self.settings.output_format == "text":
                return result.document.export_to_document_tokens()
            else:
                raise ValueError(f"Unsupported output format: {self.settings.output_format}")

        try:
            document_content = await asyncio.wait_for(
                loop.run_in_executor(None, _convert_document),
                timeout=self.settings.timeout
            )
            return document_content
        except asyncio.TimeoutError:
            raise Exception(f"Document extraction timed out after {self.settings.timeout} seconds")
        except Exception as e:
            raise Exception(f"Docling extraction error: {e}")
```

#### `infrastructure/qdrant.py`

```python
"""
Qdrant client implementation for vector search.
"""
from qdrant_client import QdrantClient
from qdrant_client.models import PointStruct, VectorParams, Distance
from sentence_transformers import SentenceTransformer
from typing import List, Dict, Any
from ..config import QdrantSettings

class QdrantClientWrapper:
    """Wrapper for Qdrant vector database client."""

    def __init__(self, settings: QdrantSettings):
        self.settings = settings
        self.client = QdrantClient(host=settings.host, port=settings.port)
        self.encoder = SentenceTransformer('all-MiniLM-L6-v2')

    async def create_collection(self, collection_name: str) -> None:
        """Create a new collection in Qdrant."""
        self.client.create_collection(
            collection_name=collection_name,
            vectors_config=VectorParams(size=384, distance=Distance.COSINE)
        )

    async def upsert_document(self, collection: str, document: str, metadata: Dict[str, Any], document_id: str) -> None:
        """Embed and store a document in Qdrant."""
        vector = self.encoder.encode(document).tolist()
        point = PointStruct(
            id=document_id,
            vector=vector,
            payload={**metadata, "document": document}
        )
        self.client.upsert(collection_name=collection, points=[point])

    async def search(self, collection: str, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Perform semantic search in Qdrant collection."""
        query_vector = self.encoder.encode(query).tolist()
        results = self.client.search(
            collection_name=collection,
            query_vector=query_vector,
            limit=limit
        )
        return [result.payload for result in results]
```

#### `infrastructure/database.py`

```python
"""
Database session management for SQLAlchemy.
"""
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from ..config import DatabaseSettings
from ..models.database import Base

class DatabaseSession:
    """Database session manager for SQLAlchemy."""

    def __init__(self, settings: DatabaseSettings):
        self.engine = create_engine(settings.url)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        Base.metadata.create_all(bind=self.engine)

    def get_session(self) -> Session:
        """Get a new database session."""
        return self.SessionLocal()
```

### 8. Utility Layer (`utils/`)

#### `utils/embedding.py`

```python
"""
Utility functions for text embedding and processing.
"""
from sentence_transformers import SentenceTransformer
from typing import List

class EmbeddingUtils:
    """Utility class for text embedding operations."""

    def __init__(self):
        self.model = SentenceTransformer('all-MiniLM-L6-v2')

    def embed_text(self, text: str) -> List[float]:
        """Embed a single text string."""
        return self.model.encode(text).tolist()

    def embed_texts(self, texts: List[str]) -> List[List[float]]:
        """Embed multiple text strings."""
        return self.model.encode(texts).tolist()
```

### 9. Test Layer (`tests/`)

#### `tests/conftest.py`

```python
"""
Pytest configuration and fixtures.
"""
import pytest
from unittest.mock import Mock, AsyncMock
from ..core import AppDependencies
from ..config import Settings

@pytest.fixture
def mock_settings():
    """Mock application settings for testing."""
    return Settings()

@pytest.fixture
def mock_dependencies(mock_settings):
    """Mock application dependencies for testing."""
    return AppDependencies(
        settings=mock_settings,
        graphdb_client=AsyncMock(),
        kafka_consumer=AsyncMock(),
        minio_client=AsyncMock(),
        qdrant_client=AsyncMock(),
        docling_client=AsyncMock(),
        db_session=Mock()
    )
```

#### `tests/test_agents.py`

```python
"""
Tests for AI agents.
"""
import pytest
from ..agents import RDFQueryAgent, RAGAgent

@pytest.mark.asyncio
async def test_rdf_query_agent(mock_dependencies):
    """Test RDF query agent functionality."""
    agent = RDFQueryAgent(mock_dependencies.settings)
    # Test agent tools and execution
    pass

@pytest.mark.asyncio
async def test_rag_agent(mock_dependencies):
    """Test RAG agent functionality."""
    agent = RAGAgent(mock_dependencies.settings)
    # Test agent tools and execution
    pass
```

#### `tests/test_api.py`

```python
"""
Tests for FastAPI endpoints.
"""
import pytest
from fastapi.testclient import TestClient
from ..api.main import app

client = TestClient(app)

def test_list_usecases():
    """Test listing available usecases."""
    response = client.get("/usecases")
    assert response.status_code == 200
```

#### `tests/test_docling.py`

```python
"""
Tests for Docling document extraction.
"""
import pytest
from unittest.mock import Mock, patch
from ..infrastructure.docling import DoclingClient
from ..config import DoclingSettings

@pytest.mark.asyncio
async def test_docling_extraction():
    """Test Docling document extraction functionality."""
    settings = DoclingSettings(enabled=True, output_format="markdown")
    client = DoclingClient(settings)

    # Mock the converter
    with patch.object(client.converter, 'convert') as mock_convert:
        mock_result = Mock()
        mock_result.document.export_to_markdown.return_value = "# Test Document\nThis is a test."
        mock_convert.return_value = mock_result

        content = await client.extract_document("https://example.com/test.pdf")
        assert content == "# Test Document\nThis is a test."
```

## Environment Variables

Create a `.env` file with the following variables:

```env
# AI Configuration
AI__API_KEY=your-openai-api-key

# Kafka Configuration
KAFKA__BOOTSTRAP_SERVERS=kafka:9092

# GraphDB Configuration
GRAPHDB__URL=http://graphdb:7200

# MinIO Configuration
MINIO__ACCESS_KEY=minioadmin
MINIO__SECRET_KEY=minioadmin

# Qdrant Configuration
QDRANT__HOST=qdrant

# Database Configuration
DATABASE__URL=sqlite:///./app.db
```

## Key Design Principles

1. **Dependency Injection**: All external dependencies are injected through the `AppDependencies` class, making the system highly testable.

2. **Separation of Concerns**: Clear separation between agents, services, API, and infrastructure layers.

3. **Configuration Management**: Centralized configuration using pydantic-settings with YAML and environment variable support.

4. **Agent Composition**: Agents can delegate to other agents, creating a flexible and composable architecture.

5. **Event-Driven**: Kafka integration allows for real-time usecase synchronization and processing.

6. **Type Safety**: Extensive use of Pydantic models for data validation and type safety.

7. **Testability**: Mockable dependencies and comprehensive test structure.

8. **Document Extraction**: Integrated Docling for intelligent document processing with configurable output formats.

## Implementation Notes

- The system uses SQLite for simplicity but can be easily switched to PostgreSQL or other databases.
- All external service clients are designed to be easily mockable for testing.
- The agent system leverages pydantic-ai's built-in retry and validation mechanisms.
- Kafka consumer runs as a background task in the FastAPI application.
- Document processing is handled asynchronously when BuildingCertificate nodes are detected.
- Docling integration supports multiple output formats (markdown, json, text) and includes timeout handling.
- Session management uses JSON fields in SQLite for conversation history storage.
- The Docling client runs synchronous operations in asyncio executors to maintain async compatibility.
