"""
Main FastAPI application for the RDF Agent backend.
Provides REST API endpoints for the agentic RDF system.
"""
import logging
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from core.dependencies import get_dependencies, cleanup_dependencies, Dependencies
from api.routes import query_routes, document_routes, session_routes, admin_routes
from config.settings import Settings


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    logger.info("Starting RDF Agent backend...")
    
    try:
        # Initialize dependencies
        deps = await get_dependencies()
        logger.info("Dependencies initialized successfully")
        
        yield
        
    except Exception as e:
        logger.error(f"Error during startup: {e}")
        raise
    finally:
        # Cleanup
        logger.info("Shutting down RDF Agent backend...")
        await cleanup_dependencies()
        logger.info("Cleanup completed")


# Create FastAPI app
app = FastAPI(
    title="RDF Agent Backend",
    description="Agentic AI system for RDF database querying and document processing",
    version="0.1.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    try:
        deps = await get_dependencies()
        
        # Check individual services
        health_status = {
            "status": "healthy",
            "services": {
                "database": True,  # Basic check - could be enhanced
                "kafka": True,     # Basic check - could be enhanced
                "graphdb": await deps.graphdb_client.health_check(),
                "qdrant": True,    # Basic check - could be enhanced
                "minio": True      # Basic check - could be enhanced
            }
        }
        
        # Overall health
        all_healthy = all(health_status["services"].values())
        health_status["status"] = "healthy" if all_healthy else "degraded"
        
        return health_status
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "error": str(e)
            }
        )


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "name": "RDF Agent Backend",
        "version": "0.1.0",
        "description": "Agentic AI system for RDF database querying and document processing",
        "endpoints": {
            "health": "/health",
            "docs": "/docs",
            "query": "/api/v1/query",
            "documents": "/api/v1/documents",
            "sessions": "/api/v1/sessions",
            "admin": "/api/v1/admin"
        }
    }


# Include routers
app.include_router(
    query_routes.router,
    prefix="/api/v1/query",
    tags=["Query"]
)

app.include_router(
    document_routes.router,
    prefix="/api/v1/documents",
    tags=["Documents"]
)

app.include_router(
    session_routes.router,
    prefix="/api/v1/sessions",
    tags=["Sessions"]
)

app.include_router(
    admin_routes.router,
    prefix="/api/v1/admin",
    tags=["Admin"]
)


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler."""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": "An unexpected error occurred"
        }
    )


# Dependency injection
async def get_deps() -> Dependencies:
    """Get dependencies for route handlers."""
    return await get_dependencies()


if __name__ == "__main__":
    import uvicorn
    
    settings = Settings()
    
    uvicorn.run(
        "api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
