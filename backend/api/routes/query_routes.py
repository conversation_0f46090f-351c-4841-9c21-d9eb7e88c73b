"""
Query routes for the RDF Agent API.
Handles natural language and SPARQL queries.
"""
import logging
import uuid
from typing import Optional

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel

from core.dependencies import get_dependencies, Dependencies
from models.data_models import QueryRequest, QueryResponse
from agents.orchestrator_agent import OrchestratorAgent


logger = logging.getLogger(__name__)

router = APIRouter()


class QueryRequestModel(BaseModel):
    """Request model for queries."""
    query_text: str
    query_type: str = "natural_language"  # or "sparql"
    session_id: Optional[str] = None
    context: dict = {}


class QueryResponseModel(BaseModel):
    """Response model for queries."""
    query_id: str
    session_id: Optional[str]
    response_text: str
    sparql_query: Optional[str] = None
    results: Optional[list] = None
    confidence: Optional[float] = None
    sources: list = []
    processing_time: Optional[float] = None
    timestamp: str


@router.post("/", response_model=QueryResponseModel)
async def process_query(
    request: QueryRequestModel,
    deps: Dependencies = Depends(get_dependencies)
):
    """Process a natural language or SPARQL query."""
    try:
        # Create query request
        query_request = QueryRequest(
            query_id=str(uuid.uuid4()),
            session_id=request.session_id,
            query_text=request.query_text,
            query_type=request.query_type,
            context=request.context
        )
        
        # Create orchestrator agent
        orchestrator = OrchestratorAgent(deps)
        
        # Process query
        response = await orchestrator.process_query(query_request)
        
        # Convert to response model
        return QueryResponseModel(
            query_id=response.query_id,
            session_id=response.session_id,
            response_text=response.response_text,
            sparql_query=response.sparql_query,
            results=response.results,
            confidence=response.confidence,
            sources=response.sources,
            processing_time=response.processing_time,
            timestamp=response.timestamp.isoformat()
        )
        
    except Exception as e:
        logger.error(f"Error processing query: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/sparql", response_model=QueryResponseModel)
async def execute_sparql(
    request: QueryRequestModel,
    deps: Dependencies = Depends(get_dependencies)
):
    """Execute a SPARQL query directly."""
    try:
        # Force query type to SPARQL
        request.query_type = "sparql"
        
        # Create query request
        query_request = QueryRequest(
            query_id=str(uuid.uuid4()),
            session_id=request.session_id,
            query_text=request.query_text,
            query_type="sparql",
            context=request.context
        )
        
        # Execute directly via GraphDB client
        result = await deps.graphdb_client.execute_sparql_query(request.query_text)
        bindings = result.get('results', {}).get('bindings', [])
        
        # Create response
        response = QueryResponse(
            query_id=query_request.query_id,
            session_id=query_request.session_id,
            response_text=f"SPARQL query executed successfully. Found {len(bindings)} results.",
            sparql_query=request.query_text,
            results=bindings
        )
        
        return QueryResponseModel(
            query_id=response.query_id,
            session_id=response.session_id,
            response_text=response.response_text,
            sparql_query=response.sparql_query,
            results=response.results,
            confidence=response.confidence,
            sources=response.sources,
            processing_time=response.processing_time,
            timestamp=response.timestamp.isoformat()
        )
        
    except Exception as e:
        logger.error(f"Error executing SPARQL query: {e}")
        raise HTTPException(status_code=500, detail=str(e))


class ConvertToSparqlRequest(BaseModel):
    """Request model for converting natural language to SPARQL."""
    query_text: str


class ConvertToSparqlResponse(BaseModel):
    """Response model for SPARQL conversion."""
    original_query: str
    sparql_query: str
    explanation: Optional[str] = None


@router.post("/convert-to-sparql", response_model=ConvertToSparqlResponse)
async def convert_to_sparql(
    request: ConvertToSparqlRequest,
    deps: Dependencies = Depends(get_dependencies)
):
    """Convert a natural language query to SPARQL."""
    try:
        from agents.rdf_query_agent import RDFQueryAgent
        
        # Create RDF query agent
        rdf_agent = RDFQueryAgent(deps)
        
        # Convert to SPARQL
        sparql_query = await rdf_agent.convert_to_sparql(request.query_text)
        
        return ConvertToSparqlResponse(
            original_query=request.query_text,
            sparql_query=sparql_query,
            explanation=f"Converted natural language query to SPARQL"
        )
        
    except Exception as e:
        logger.error(f"Error converting to SPARQL: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/repository/info")
async def get_repository_info(deps: Dependencies = Depends(get_dependencies)):
    """Get information about the RDF repository."""
    try:
        size = await deps.graphdb_client.get_repository_size()
        repositories = await deps.graphdb_client.get_repositories()
        
        return {
            "repository_size": size,
            "available_repositories": repositories,
            "default_repository": deps.settings.graphdb.default_repository
        }
        
    except Exception as e:
        logger.error(f"Error getting repository info: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/examples")
async def get_query_examples():
    """Get example queries for testing."""
    return {
        "natural_language_examples": [
            "Find all buildings in Berlin",
            "Show me addresses with postal code 10115",
            "What buildings are located near coordinates 52.5, 13.4?",
            "List all buildings with their addresses",
            "Find buildings that have a name containing 'office'"
        ],
        "sparql_examples": [
            {
                "description": "Find all buildings",
                "query": """
                PREFIX building: <http://example.org/building/>
                PREFIX schema: <http://schema.org/>
                
                SELECT ?building ?name WHERE {
                    ?building a schema:Building .
                    OPTIONAL { ?building schema:name ?name }
                }
                LIMIT 10
                """
            },
            {
                "description": "Find addresses in a specific city",
                "query": """
                PREFIX address: <http://example.org/address/>
                PREFIX schema: <http://schema.org/>
                
                SELECT ?address ?street ?city WHERE {
                    ?address a schema:PostalAddress .
                    ?address schema:addressLocality ?city .
                    ?address schema:streetAddress ?street .
                    FILTER(?city = "Berlin")
                }
                LIMIT 10
                """
            }
        ]
    }
