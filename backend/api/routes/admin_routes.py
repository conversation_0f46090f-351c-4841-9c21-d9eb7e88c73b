"""
Admin routes for the RDF Agent API.
Handles administrative operations and system management.
"""
import logging
from typing import Optional

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel

from core.dependencies import get_dependencies, Dependencies


logger = logging.getLogger(__name__)

router = APIRouter()


class SystemStatusResponse(BaseModel):
    """Response model for system status."""
    status: str
    services: dict
    version: str
    uptime: Optional[str] = None


class TTLUploadRequest(BaseModel):
    """Request model for uploading TTL data."""
    ttl_content: str
    context: Optional[str] = None
    repository: Optional[str] = None


@router.get("/status", response_model=SystemStatusResponse)
async def get_system_status(deps: Dependencies = Depends(get_dependencies)):
    """Get comprehensive system status."""
    try:
        # Check all services
        services_status = {
            "database": True,  # Basic check
            "kafka": True,     # Basic check
            "graphdb": await deps.graphdb_client.health_check(),
            "minio": True,     # Basic check
            "qdrant": True,    # Basic check
            "docling": await deps.docling_service.health_check()
        }
        
        # Overall status
        all_healthy = all(services_status.values())
        overall_status = "healthy" if all_healthy else "degraded"
        
        return SystemStatusResponse(
            status=overall_status,
            services=services_status,
            version="0.1.0"
        )
        
    except Exception as e:
        logger.error(f"Error getting system status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/repositories")
async def list_repositories(deps: Dependencies = Depends(get_dependencies)):
    """List all available RDF repositories."""
    try:
        repositories = await deps.graphdb_client.get_repositories()
        default_repo = deps.settings.graphdb.default_repository
        
        return {
            "repositories": repositories,
            "default_repository": default_repo
        }
        
    except Exception as e:
        logger.error(f"Error listing repositories: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/repositories/{repository}/size")
async def get_repository_size(
    repository: str,
    deps: Dependencies = Depends(get_dependencies)
):
    """Get the size of a specific repository."""
    try:
        size = await deps.graphdb_client.get_repository_size(repository)
        
        return {
            "repository": repository,
            "size": size,
            "unit": "triples"
        }
        
    except Exception as e:
        logger.error(f"Error getting repository size: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/repositories/{repository}/upload-ttl")
async def upload_ttl_data(
    repository: str,
    request: TTLUploadRequest,
    deps: Dependencies = Depends(get_dependencies)
):
    """Upload TTL data to a repository."""
    try:
        success = await deps.graphdb_client.upload_ttl_data(
            ttl_content=request.ttl_content,
            repository=repository,
            context=request.context
        )
        
        if not success:
            raise HTTPException(status_code=500, detail="Failed to upload TTL data")
        
        return {
            "repository": repository,
            "message": "TTL data uploaded successfully",
            "context": request.context
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading TTL data: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/repositories/{repository}/clear")
async def clear_repository(
    repository: str,
    deps: Dependencies = Depends(get_dependencies)
):
    """Clear all data from a repository."""
    try:
        success = await deps.graphdb_client.clear_repository(repository)
        
        if not success:
            raise HTTPException(status_code=500, detail="Failed to clear repository")
        
        return {
            "repository": repository,
            "message": "Repository cleared successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error clearing repository: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/vector-db/stats")
async def get_vector_db_stats(deps: Dependencies = Depends(get_dependencies)):
    """Get vector database statistics."""
    try:
        count = await deps.qdrant_client.count_points()
        collection_name = deps.qdrant_client.collection_name
        
        return {
            "collection_name": collection_name,
            "total_points": count,
            "status": "healthy"
        }
        
    except Exception as e:
        logger.error(f"Error getting vector DB stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/object-storage/stats")
async def get_object_storage_stats(deps: Dependencies = Depends(get_dependencies)):
    """Get object storage statistics."""
    try:
        # List all objects
        objects = await deps.minio_client.list_objects()
        
        # Count by prefix
        document_objects = [obj for obj in objects if obj.startswith("documents/")]
        
        return {
            "bucket_name": deps.minio_client.bucket_name,
            "total_objects": len(objects),
            "document_objects": len(document_objects),
            "status": "healthy"
        }
        
    except Exception as e:
        logger.error(f"Error getting object storage stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/kafka/topics")
async def list_kafka_topics(deps: Dependencies = Depends(get_dependencies)):
    """List available Kafka topics."""
    try:
        topics = await deps.kafka_client.get_topics()
        
        return {
            "topics": topics,
            "count": len(topics)
        }
        
    except Exception as e:
        logger.error(f"Error listing Kafka topics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/settings")
async def get_system_settings(deps: Dependencies = Depends(get_dependencies)):
    """Get system configuration settings (sanitized)."""
    try:
        settings = deps.settings
        
        # Return sanitized settings (no secrets)
        return {
            "kafka": {
                "bootstrap_servers": settings.kafka.bootstrap_servers,
                "group_id": settings.kafka.group_id
            },
            "graphdb": {
                "url": settings.graphdb.url,
                "default_repository": settings.graphdb.default_repository
            },
            "minio": {
                "endpoint": settings.minio.endpoint,
                "bucket_name": settings.minio.bucket_name
            },
            "qdrant": {
                "host": settings.qdrant.host,
                "port": settings.qdrant.port,
                "collection_name": settings.qdrant.collection_name
            },
            "docling": {
                "enabled": settings.docling.enabled,
                "output_format": settings.docling.output_format
            },
            "database": {
                "url": settings.database.url.replace(settings.database.url.split("///")[1], "***") if "///" in settings.database.url else "***"
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting system settings: {e}")
        raise HTTPException(status_code=500, detail=str(e))


class TestConnectionRequest(BaseModel):
    """Request model for testing connections."""
    service: str  # kafka, graphdb, minio, qdrant


@router.post("/test-connection")
async def test_service_connection(
    request: TestConnectionRequest,
    deps: Dependencies = Depends(get_dependencies)
):
    """Test connection to a specific service."""
    try:
        service = request.service.lower()
        
        if service == "graphdb":
            success = await deps.graphdb_client.health_check()
        elif service == "kafka":
            # Basic test - could be enhanced
            success = deps.kafka_client is not None
        elif service == "minio":
            # Basic test - could be enhanced
            success = deps.minio_client is not None
        elif service == "qdrant":
            # Basic test - could be enhanced
            success = deps.qdrant_client is not None
        else:
            raise HTTPException(status_code=400, detail=f"Unknown service: {service}")
        
        return {
            "service": service,
            "status": "connected" if success else "disconnected",
            "success": success
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error testing service connection: {e}")
        raise HTTPException(status_code=500, detail=str(e))
