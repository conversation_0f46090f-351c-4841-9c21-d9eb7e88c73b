"""
Document routes for the RDF Agent API.
Handles document processing, storage, and retrieval.
"""
import logging
import uuid
from typing import Optional, List

from fastapi import APIRouter, HTTPException, Depends, UploadFile, File
from pydantic import BaseModel

from core.dependencies import get_dependencies, Dependencies
from models.data_models import DocumentInfo, ProcessingTask, ProcessingStatus
from agents.rag_agent import RAGAgent


logger = logging.getLogger(__name__)

router = APIRouter()


class DocumentSearchRequest(BaseModel):
    """Request model for document search."""
    query: str
    limit: int = 5
    score_threshold: float = 0.7


class DocumentSearchResponse(BaseModel):
    """Response model for document search."""
    query: str
    results: List[dict]
    count: int
    processing_time: Optional[float] = None


class ProcessingTaskResponse(BaseModel):
    """Response model for processing tasks."""
    task_id: str
    document_id: str
    filename: str
    status: str
    created_at: str
    updated_at: str
    error_message: Optional[str] = None
    result: Optional[dict] = None


@router.post("/search", response_model=DocumentSearchResponse)
async def search_documents(
    request: DocumentSearchRequest,
    deps: Dependencies = Depends(get_dependencies)
):
    """Search documents using semantic similarity."""
    try:
        # Create RAG agent
        rag_agent = RAGAgent(deps)
        
        # Search documents
        result = await rag_agent.search_documents(
            query=request.query,
            limit=request.limit
        )
        
        return DocumentSearchResponse(
            query=request.query,
            results=result.get("results", []),
            count=len(result.get("results", [])),
            processing_time=result.get("processing_time")
        )
        
    except Exception as e:
        logger.error(f"Error searching documents: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/upload")
async def upload_document(
    file: UploadFile = File(...),
    deps: Dependencies = Depends(get_dependencies)
):
    """Upload and process a document."""
    try:
        # Generate document ID
        document_id = str(uuid.uuid4())
        
        # Read file content
        file_content = await file.read()
        
        # Create document info
        document_info = DocumentInfo(
            document_id=document_id,
            filename=file.filename,
            file_path=f"uploads/{document_id}_{file.filename}",
            mime_type=file.content_type or "application/octet-stream",
            size=len(file_content)
        )
        
        # Create processing task
        task_id = str(uuid.uuid4())
        task = ProcessingTask(
            task_id=task_id,
            document_info=document_info,
            status=ProcessingStatus.PENDING
        )
        
        # Save task to database
        await deps.database_manager.create_processing_task(task)
        
        # Upload to MinIO
        object_name = f"documents/{document_id}/{file.filename}"
        upload_success = await deps.minio_client.upload_data(
            data=file_content,
            object_name=object_name,
            content_type=file.content_type or "application/octet-stream"
        )
        
        if not upload_success:
            raise HTTPException(status_code=500, detail="Failed to upload file to storage")
        
        # TODO: Trigger async processing (could be done via Kafka or background task)
        # For now, we'll just return the task info
        
        return {
            "task_id": task_id,
            "document_id": document_id,
            "filename": file.filename,
            "status": "pending",
            "message": "Document uploaded successfully. Processing will begin shortly."
        }
        
    except Exception as e:
        logger.error(f"Error uploading document: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/{task_id}", response_model=ProcessingTaskResponse)
async def get_processing_task(
    task_id: str,
    deps: Dependencies = Depends(get_dependencies)
):
    """Get the status of a processing task."""
    try:
        # Query database for task
        # Note: This would require implementing a get_processing_task method
        # For now, we'll return a placeholder response
        
        return ProcessingTaskResponse(
            task_id=task_id,
            document_id="placeholder",
            filename="placeholder.pdf",
            status="pending",
            created_at="2024-01-01T00:00:00Z",
            updated_at="2024-01-01T00:00:00Z"
        )
        
    except Exception as e:
        logger.error(f"Error getting processing task: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/collection/stats")
async def get_collection_stats(deps: Dependencies = Depends(get_dependencies)):
    """Get statistics about the document collection."""
    try:
        # Get Qdrant collection stats
        count = await deps.qdrant_client.count_points()
        
        # Get MinIO stats
        objects = await deps.minio_client.list_objects(prefix="documents/")
        
        return {
            "vector_database": {
                "total_documents": count,
                "collection_name": deps.qdrant_client.collection_name
            },
            "object_storage": {
                "total_objects": len(objects),
                "bucket_name": deps.minio_client.bucket_name
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting collection stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{document_id}")
async def get_document(
    document_id: str,
    deps: Dependencies = Depends(get_dependencies)
):
    """Get a specific document by ID."""
    try:
        # Get from Qdrant
        rag_agent = RAGAgent(deps)
        point = await deps.qdrant_client.get_point(document_id)
        
        if not point:
            raise HTTPException(status_code=404, detail="Document not found")
        
        return {
            "document_id": document_id,
            "content": point.get("payload", {}),
            "metadata": point.get("payload", {}).get("metadata", {})
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting document: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{document_id}")
async def delete_document(
    document_id: str,
    deps: Dependencies = Depends(get_dependencies)
):
    """Delete a document from the system."""
    try:
        # Delete from Qdrant
        rag_agent = RAGAgent(deps)
        success = await rag_agent.delete_document(document_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="Document not found or could not be deleted")
        
        # TODO: Also delete from MinIO if needed
        
        return {
            "document_id": document_id,
            "message": "Document deleted successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting document: {e}")
        raise HTTPException(status_code=500, detail=str(e))


class AddDocumentRequest(BaseModel):
    """Request model for adding a document directly."""
    document_id: str
    content: str
    metadata: Optional[dict] = None


@router.post("/add")
async def add_document(
    request: AddDocumentRequest,
    deps: Dependencies = Depends(get_dependencies)
):
    """Add a document directly to the vector database."""
    try:
        # Create RAG agent
        rag_agent = RAGAgent(deps)
        
        # Add document
        success = await rag_agent.add_document(
            document_id=request.document_id,
            content=request.content,
            metadata=request.metadata
        )
        
        if not success:
            raise HTTPException(status_code=500, detail="Failed to add document")
        
        return {
            "document_id": request.document_id,
            "message": "Document added successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adding document: {e}")
        raise HTTPException(status_code=500, detail=str(e))
