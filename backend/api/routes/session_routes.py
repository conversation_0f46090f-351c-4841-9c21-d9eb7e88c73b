"""
Session routes for the RDF Agent API.
Handles user sessions and conversation history.
"""
import logging
from typing import Optional, List

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel

from core.dependencies import get_dependencies, Dependencies
from models.data_models import Session, ConversationTurn


logger = logging.getLogger(__name__)

router = APIRouter()


class CreateSessionRequest(BaseModel):
    """Request model for creating a session."""
    metadata: Optional[dict] = None


class SessionResponse(BaseModel):
    """Response model for sessions."""
    session_id: str
    created_at: str
    updated_at: str
    metadata: dict
    turn_count: Optional[int] = None


class ConversationTurnResponse(BaseModel):
    """Response model for conversation turns."""
    turn_id: str
    session_id: str
    user_message: str
    assistant_response: str
    timestamp: str
    metadata: dict


class SessionSummaryResponse(BaseModel):
    """Response model for session summary."""
    session_id: str
    created_at: str
    updated_at: str
    total_turns: int
    metadata: dict
    first_message_at: Optional[str] = None
    last_message_at: Optional[str] = None
    total_user_characters: Optional[int] = None
    total_assistant_characters: Optional[int] = None


@router.post("/", response_model=SessionResponse)
async def create_session(
    request: CreateSessionRequest,
    deps: Dependencies = Depends(get_dependencies)
):
    """Create a new session."""
    try:
        session_id = await deps.session_manager.create_session(request.metadata)
        
        # Get the created session
        session = await deps.session_manager.get_session(session_id)
        
        if not session:
            raise HTTPException(status_code=500, detail="Failed to create session")
        
        return SessionResponse(
            session_id=session.session_id,
            created_at=session.created_at.isoformat(),
            updated_at=session.updated_at.isoformat(),
            metadata=session.metadata,
            turn_count=len(session.turns)
        )
        
    except Exception as e:
        logger.error(f"Error creating session: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{session_id}", response_model=SessionResponse)
async def get_session(
    session_id: str,
    deps: Dependencies = Depends(get_dependencies)
):
    """Get a session by ID."""
    try:
        session = await deps.session_manager.get_session(session_id)
        
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        return SessionResponse(
            session_id=session.session_id,
            created_at=session.created_at.isoformat(),
            updated_at=session.updated_at.isoformat(),
            metadata=session.metadata,
            turn_count=len(session.turns)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting session: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{session_id}/history")
async def get_conversation_history(
    session_id: str,
    limit: Optional[int] = None,
    deps: Dependencies = Depends(get_dependencies)
):
    """Get conversation history for a session."""
    try:
        turns = await deps.session_manager.get_conversation_history(
            session_id, limit=limit
        )
        
        return {
            "session_id": session_id,
            "turns": [
                ConversationTurnResponse(
                    turn_id=turn.turn_id,
                    session_id=turn.session_id,
                    user_message=turn.user_message,
                    assistant_response=turn.assistant_response,
                    timestamp=turn.timestamp.isoformat(),
                    metadata=turn.metadata
                )
                for turn in turns
            ],
            "count": len(turns)
        }
        
    except Exception as e:
        logger.error(f"Error getting conversation history: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{session_id}/context")
async def get_session_context(
    session_id: str,
    max_turns: int = 5,
    deps: Dependencies = Depends(get_dependencies)
):
    """Get recent conversation context for a session."""
    try:
        context = await deps.session_manager.get_recent_context(
            session_id, max_turns=max_turns
        )
        
        return {
            "session_id": session_id,
            "context": context,
            "max_turns": max_turns
        }
        
    except Exception as e:
        logger.error(f"Error getting session context: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{session_id}/summary", response_model=SessionSummaryResponse)
async def get_session_summary(
    session_id: str,
    deps: Dependencies = Depends(get_dependencies)
):
    """Get a summary of session statistics."""
    try:
        summary = await deps.session_manager.get_session_summary(session_id)
        
        if not summary:
            raise HTTPException(status_code=404, detail="Session not found")
        
        return SessionSummaryResponse(**summary)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting session summary: {e}")
        raise HTTPException(status_code=500, detail=str(e))


class UpdateSessionMetadataRequest(BaseModel):
    """Request model for updating session metadata."""
    metadata: dict


@router.patch("/{session_id}/metadata")
async def update_session_metadata(
    session_id: str,
    request: UpdateSessionMetadataRequest,
    deps: Dependencies = Depends(get_dependencies)
):
    """Update session metadata."""
    try:
        success = await deps.session_manager.update_session_metadata(
            session_id, request.metadata
        )
        
        if not success:
            raise HTTPException(status_code=404, detail="Session not found")
        
        return {
            "session_id": session_id,
            "message": "Metadata updated successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating session metadata: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/")
async def list_sessions(
    limit: Optional[int] = 50,
    offset: int = 0,
    deps: Dependencies = Depends(get_dependencies)
):
    """List all sessions with basic information."""
    try:
        sessions = await deps.session_manager.get_all_sessions(
            limit=limit, offset=offset
        )
        
        return {
            "sessions": [
                SessionResponse(
                    session_id=session["session_id"],
                    created_at=session["created_at"],
                    updated_at=session["updated_at"],
                    metadata=session["metadata"],
                    turn_count=session["turn_count"]
                )
                for session in sessions
            ],
            "count": len(sessions),
            "limit": limit,
            "offset": offset
        }
        
    except Exception as e:
        logger.error(f"Error listing sessions: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/cleanup")
async def cleanup_old_sessions(
    days_old: int = 30,
    deps: Dependencies = Depends(get_dependencies)
):
    """Clean up sessions older than specified days."""
    try:
        deleted_count = await deps.session_manager.cleanup_old_sessions(days_old)
        
        return {
            "message": f"Cleaned up {deleted_count} old sessions",
            "deleted_count": deleted_count,
            "days_old": days_old
        }
        
    except Exception as e:
        logger.error(f"Error cleaning up old sessions: {e}")
        raise HTTPException(status_code=500, detail=str(e))
