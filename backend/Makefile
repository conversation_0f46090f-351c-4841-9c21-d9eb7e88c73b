# Makefile for RDF Agent System Testing and Development

.PHONY: help install test test-unit test-integration test-e2e test-all test-coverage clean-test-data lint format check-types dev-setup

# Default target
help:
	@echo "RDF Agent System - Development Commands"
	@echo "======================================"
	@echo ""
	@echo "Setup Commands:"
	@echo "  install          Install dependencies"
	@echo "  dev-setup        Setup development environment"
	@echo ""
	@echo "Testing Commands:"
	@echo "  test-unit        Run unit tests only"
	@echo "  test-integration Run integration tests only"
	@echo "  test-e2e         Run end-to-end tests only"
	@echo "  test-all         Run all tests"
	@echo "  test-coverage    Generate coverage report"
	@echo "  clean-test-data  Clean up test data"
	@echo ""
	@echo "Code Quality Commands:"
	@echo "  lint             Run linting (black + isort)"
	@echo "  format           Format code (black + isort)"
	@echo "  check-types      Run type checking (mypy)"
	@echo ""
	@echo "Service Commands:"
	@echo "  start-services   Start required services (Docker)"
	@echo "  stop-services    Stop services"
	@echo "  restart-services Restart services"
	@echo ""
	@echo "Examples:"
	@echo "  make test-unit           # Quick unit tests"
	@echo "  make test-all            # Full test suite"
	@echo "  make clean-test-data     # Clean before testing"
	@echo "  make format lint         # Format and lint code"

# Installation and setup
install:
	@echo "📦 Installing dependencies..."
	uv sync --dev

dev-setup: install
	@echo "🔧 Setting up development environment..."
	uv run pre-commit install
	@echo "✅ Development environment ready!"

# Testing commands
test-unit:
	@echo "🧪 Running unit tests..."
	uv run python scripts/run_tests.py unit --verbose

test-integration:
	@echo "🔗 Running integration tests..."
	uv run python scripts/run_tests.py integration --verbose

test-e2e:
	@echo "🎯 Running end-to-end tests..."
	uv run python scripts/run_tests.py e2e --verbose

test-all:
	@echo "🚀 Running all tests..."
	uv run python scripts/run_tests.py all --verbose

test-coverage:
	@echo "📈 Generating coverage report..."
	uv run python scripts/run_tests.py coverage
	@echo "📊 Coverage report available at: htmlcov/index.html"

test-quick:
	@echo "⚡ Running quick tests (unit only, no cleanup)..."
	uv run python scripts/run_tests.py unit --no-cleanup

test-ci:
	@echo "🤖 Running CI test suite..."
	uv run python scripts/run_tests.py all --no-cleanup

# Test data management
clean-test-data:
	@echo "🧹 Cleaning test data..."
	uv run python scripts/cleanup_test_data.py all

clean-minio:
	@echo "🗂️ Cleaning MinIO test data..."
	uv run python scripts/cleanup_test_data.py minio

clean-qdrant:
	@echo "🔍 Cleaning Qdrant test data..."
	uv run python scripts/cleanup_test_data.py qdrant

clean-graphdb:
	@echo "🕸️ Cleaning GraphDB test data..."
	uv run python scripts/cleanup_test_data.py graphdb

# Code quality
format:
	@echo "🎨 Formatting code..."
	uv run black .
	uv run isort .

lint:
	@echo "🔍 Linting code..."
	uv run black --check .
	uv run isort --check-only .

check-types:
	@echo "🔬 Type checking..."
	uv run mypy .

quality-check: format lint check-types
	@echo "✅ Code quality checks completed!"

# Service management (requires Docker)
start-services:
	@echo "🐳 Starting required services..."
	@if [ -f docker-compose.yml ]; then \
		docker-compose up -d; \
	else \
		echo "⚠️ docker-compose.yml not found. Please ensure services are running manually."; \
	fi

stop-services:
	@echo "🛑 Stopping services..."
	@if [ -f docker-compose.yml ]; then \
		docker-compose down; \
	else \
		echo "⚠️ docker-compose.yml not found."; \
	fi

restart-services: stop-services start-services

# Development workflow
dev-test: clean-test-data test-unit
	@echo "🔄 Development test cycle completed!"

full-check: format lint check-types test-all
	@echo "🎉 Full quality and test check completed!"

# Specific test categories
test-agents:
	@echo "🤖 Testing agents..."
	uv run pytest tests/unit/test_*_agent.py -v

test-services:
	@echo "⚙️ Testing services..."
	uv run pytest tests/integration/test_*_service.py -v

test-infrastructure:
	@echo "🏗️ Testing infrastructure..."
	uv run pytest tests/integration/test_*_client.py -v

# Performance and load testing
test-performance:
	@echo "⚡ Running performance tests..."
	uv run pytest tests/e2e/test_complete_system.py::TestCompleteSystemE2E::test_performance_under_load -v

test-load:
	@echo "🏋️ Running load tests..."
	uv run pytest -m "e2e" --timeout=1200 -v

# Debugging and development
test-debug:
	@echo "🐛 Running tests in debug mode..."
	uv run pytest --pdb --pdbcls=IPython.terminal.debugger:Pdb -v

test-failed:
	@echo "🔄 Re-running failed tests..."
	uv run pytest --lf -v

test-watch:
	@echo "👀 Running tests in watch mode..."
	uv run pytest-watch

# Documentation and reporting
test-report:
	@echo "📋 Generating test report..."
	uv run pytest --html=test-report.html --self-contained-html tests/

# Cleanup commands
clean-all: clean-test-data
	@echo "🧹 Cleaning all generated files..."
	rm -rf htmlcov/
	rm -rf .pytest_cache/
	rm -rf __pycache__/
	rm -rf .coverage
	rm -rf coverage.xml
	rm -rf test-report.html
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name "*.pyc" -delete 2>/dev/null || true

# Environment checks
check-env:
	@echo "🔍 Checking environment..."
	@echo "Python version: $(shell python --version)"
	@echo "UV version: $(shell uv --version)"
	@echo "Current directory: $(shell pwd)"
	@echo "Environment variables:"
	@env | grep -E "(AI__|MINIO|QDRANT|GRAPHDB|KAFKA)" || echo "No relevant environment variables found"

# Quick development commands
quick-test: test-unit
dev: format lint test-unit
ci: format lint check-types test-all

# Help for specific commands
help-testing:
	@echo "Testing Command Details:"
	@echo "======================="
	@echo ""
	@echo "test-unit:        Fast unit tests with mocking"
	@echo "test-integration: Tests with real services"
	@echo "test-e2e:         Full system tests"
	@echo "test-all:         Complete test suite"
	@echo "test-coverage:    Generate HTML coverage report"
	@echo ""
	@echo "Test markers available:"
	@echo "  unit              Unit tests only"
	@echo "  integration       Integration tests only"
	@echo "  e2e               End-to-end tests only"
	@echo "  requires_services Tests needing external services"
	@echo "  requires_ai       Tests needing AI model access"
	@echo "  slow              Slow-running tests"

help-services:
	@echo "Service Management:"
	@echo "=================="
	@echo ""
	@echo "Required services for testing:"
	@echo "  - MinIO (object storage)"
	@echo "  - Qdrant (vector database)"
	@echo "  - GraphDB (RDF database)"
	@echo "  - Kafka (message queue)"
	@echo ""
	@echo "Use 'make start-services' to start with Docker"
	@echo "Or configure services manually and set environment variables"
