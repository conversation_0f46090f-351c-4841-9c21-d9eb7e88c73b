# Kafka Processor Shutdown Performance Improvements

## Problem Analysis

The Kafka processor was experiencing extremely slow shutdown times, particularly when using Ctrl+C to terminate the process. The main issues identified were:

1. **Improper Signal Handling**: The signal handler in `main.py` called `sys.exit(0)` immediately after creating a cleanup task, not waiting for proper cleanup
2. **Incomplete Kafka Consumer Shutdown**: The Kafka client's `close()` method didn't properly handle consumer loop termination
3. **Missing Graceful Shutdown**: The `KafkaProcessor.stop()` method only set a flag but didn't actually stop the consumer loop
4. **Blocking Operations**: The consumer loop could block indefinitely waiting for messages
5. **Sequential Dependency Cleanup**: Dependencies were cleaned up one by one, causing cumulative delays

## Solutions Implemented

### 1. Fixed Signal Handling (`main.py`)

**Before:**
```python
def signal_handler(signum, frame):
    logger.info(f"Received signal {signum}, shutting down...")
    if processor:
        asyncio.create_task(processor.stop())
    sys.exit(0)  # ❌ Immediate exit without waiting for cleanup
```

**After:**
```python
async def run_processor():
    shutdown_event = asyncio.Event()
    
    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        shutdown_event.set()  # ✅ Signal shutdown event
    
    # Wait for shutdown signal or processor completion
    done, pending = await asyncio.wait(
        [processor_task, asyncio.create_task(shutdown_event.wait())],
        return_when=asyncio.FIRST_COMPLETED
    )
    
    # Cancel pending tasks and wait for cleanup
    for task in pending:
        task.cancel()
        try:
            await task
        except asyncio.CancelledError:
            pass
```

### 2. Enhanced Kafka Consumer Shutdown (`infrastructure/kafka_client.py`)

**Improvements:**
- Added timeout handling for consumer and producer shutdown
- Added proper exception handling for cancellation
- Added detailed logging for shutdown progress

```python
async def close(self):
    """Close Kafka connections."""
    self._running = False
    logger.info("Closing Kafka client...")
    
    # Close consumer with timeout
    if self.consumer:
        try:
            await asyncio.wait_for(self.consumer.stop(), timeout=10.0)
            logger.info("Kafka consumer stopped")
        except asyncio.TimeoutError:
            logger.warning("Kafka consumer stop timed out")
        except Exception as e:
            logger.warning(f"Error stopping Kafka consumer: {e}")
    
    # Close producer with timeout
    if self.producer:
        try:
            await asyncio.wait_for(self.producer.stop(), timeout=5.0)
            logger.info("Kafka producer stopped")
        except asyncio.TimeoutError:
            logger.warning("Kafka producer stop timed out")
        except Exception as e:
            logger.warning(f"Error stopping Kafka producer: {e}")
```

### 3. Improved KafkaProcessor Shutdown (`services/kafka_processor.py`)

**Key Changes:**
- Added consumer task tracking
- Implemented proper task cancellation
- Added Kafka client shutdown in stop method

```python
async def start(self):
    # Start consuming in a separate task
    self._consumer_task = asyncio.create_task(self.deps.kafka_client.start_consuming())
    
    try:
        await self._consumer_task
    except asyncio.CancelledError:
        logger.info("Kafka consumer task was cancelled")

async def stop(self):
    self._running = False
    
    # Cancel the consumer task if it's running
    if self._consumer_task and not self._consumer_task.done():
        logger.info("Cancelling Kafka consumer task...")
        self._consumer_task.cancel()
        try:
            await self._consumer_task
        except asyncio.CancelledError:
            logger.info("Kafka consumer task cancelled successfully")
    
    # Stop the Kafka client
    if self.deps.kafka_client:
        await self.deps.kafka_client.close()
```

### 4. Parallel Dependency Cleanup (`core/dependencies.py`)

**Before:** Sequential cleanup (slow)
```python
async def cleanup(self):
    try:
        if self.kafka_client:
            await self.kafka_client.close()  # Wait for this to complete
    except Exception as e:
        logger.warning(f"Error closing Kafka client: {e}")
    
    try:
        if self.graphdb_client:
            await self.graphdb_client.close()  # Then wait for this
    except Exception as e:
        logger.warning(f"Error closing GraphDB client: {e}")
    # ... more sequential operations
```

**After:** Parallel cleanup with timeouts (fast)
```python
async def cleanup(self):
    # Create cleanup tasks for all clients
    cleanup_tasks = []
    
    if self.kafka_client:
        cleanup_tasks.append(self._safe_cleanup("Kafka", self.kafka_client.close(), 15.0))
    
    if self.graphdb_client:
        cleanup_tasks.append(self._safe_cleanup("GraphDB", self.graphdb_client.close(), 10.0))
    
    # ... more tasks
    
    # Run all cleanup tasks in parallel
    if cleanup_tasks:
        await asyncio.gather(*cleanup_tasks, return_exceptions=True)

async def _safe_cleanup(self, service_name: str, cleanup_coro, timeout: float):
    """Safely cleanup a service with timeout and error handling."""
    try:
        await asyncio.wait_for(cleanup_coro, timeout=timeout)
        logger.info(f"{service_name} client closed successfully")
    except asyncio.TimeoutError:
        logger.warning(f"{service_name} client cleanup timed out after {timeout}s")
    except Exception as e:
        logger.warning(f"Error closing {service_name} client: {e}")
```

## Performance Impact

### Expected Improvements:
1. **Signal Response**: Immediate response to Ctrl+C instead of waiting for current message processing
2. **Parallel Cleanup**: Dependencies now clean up simultaneously instead of sequentially
3. **Timeout Protection**: No more hanging on unresponsive services
4. **Task Cancellation**: Proper cancellation of running tasks prevents blocking

### Timeout Configuration:
- Kafka client: 15 seconds (longest timeout due to potential network delays)
- GraphDB client: 10 seconds
- MinIO, Qdrant, Database: 5 seconds each

## Testing

Use the provided test script to verify improvements:

```bash
cd backend
python test_shutdown.py
```

The script will:
1. Start the Kafka processor
2. Wait for Ctrl+C
3. Measure and report shutdown times for each component
4. Display total shutdown time

## Expected Results

**Before improvements:** 30-60+ seconds shutdown time
**After improvements:** 5-15 seconds shutdown time (depending on service responsiveness)

The improvements ensure that:
- Shutdown never hangs indefinitely
- Multiple services clean up in parallel
- Proper error handling prevents one failing service from blocking others
- Clear logging shows shutdown progress
