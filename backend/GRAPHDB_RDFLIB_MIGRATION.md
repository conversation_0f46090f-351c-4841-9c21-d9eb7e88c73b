# GraphDB Client Migration to RDFLib

## Overview

This document describes the migration from a GraphDB-specific HTTP client implementation to a generic RDFLib-based triple store client that can work with any SPARQL-compatible triple store.

## Problem Statement

The original GraphDB client implementation had several issues:
- **GraphDB-specific**: Used GraphDB's proprietary REST API endpoints instead of standard SPARQL protocol
- **Not portable**: Could not be easily switched to other triple stores (Fuseki, Stardog, etc.)
- **HTTP-based**: Direct HTTP calls without proper SPARQL abstraction
- **Limited functionality**: Missing standard SPARQL features and error handling

## Solution Architecture

### 1. Generic Triple Store Interface

Created an abstract interface (`TripleStoreInterface`) that defines standard operations:
- SPARQL query execution
- SPARQL update operations
- TTL data upload
- Repository management
- Health checks

### 2. RDFLib Implementation

Implemented the interface using RDFLib and SPARQLWrapper:
- **RDFLib**: Python library for working with RDF data
- **SPARQLWrapper**: Reliable SPARQL endpoint communication
- **Standard SPARQL Protocol**: Uses W3C SPARQL 1.1 Protocol instead of proprietary APIs

### 3. Backward Compatibility Layer

Created a new GraphDB client that:
- Maintains the same API as the original client
- Converts between old and new data formats
- Preserves all existing functionality
- Requires no changes to dependent code

## Implementation Details

### Files Created/Modified

#### New Files:
- `infrastructure/triple_store_interface.py` - Abstract interface and data models
- `infrastructure/rdflib_triple_store.py` - RDFLib-based implementation
- `infrastructure/graphdb_client_new.py` - Backward-compatible GraphDB client
- `tests/unit/test_triple_store_interface.py` - Unit tests
- `tests/integration/test_new_graphdb_client.py` - Integration tests

#### Modified Files:
- `core/dependencies.py` - Updated import to use new client
- `config/settings.py` - Enhanced with generic triple store settings
- `config/config.yaml` - Updated repository name to PortfolioExample
- `.env` - Updated repository configuration

### Key Features

#### 1. Multi-Store Support
The new implementation supports different triple store types:
- **GraphDB**: `http://localhost:7200/repositories/{repo}`
- **Fuseki**: `http://localhost:3030/{repo}/sparql`
- **Stardog**: `http://localhost:5820/{repo}/query`
- **Generic**: Any SPARQL 1.1 compliant endpoint

#### 2. Enhanced Configuration
```python
class GraphDBSettings(BaseSettings):
    url: str = "http://localhost:7200"
    default_repository: str = "PortfolioExample"
    store_type: str = "graphdb"  # graphdb, fuseki, stardog, etc.
    username: Optional[str] = None
    password: Optional[str] = None
    timeout: int = 30
```

#### 3. Standardized Results
All operations return standardized result objects:
- `SPARQLResult` - For query results with success status, data, and error handling
- `SPARQLUpdateResult` - For update operations with success status and error messages

#### 4. Async Support
Full async/await support with proper resource management:
```python
async with GraphDBClient(settings) as client:
    result = await client.execute_sparql_query("SELECT ?s ?p ?o WHERE { ?s ?p ?o }")
```

## Migration Benefits

### 1. Portability
- Can switch between different triple stores without code changes
- Standard SPARQL protocol ensures compatibility
- Configuration-driven store selection

### 2. Reliability
- Better error handling and connection management
- Proper async resource cleanup
- Standardized result formats

### 3. Maintainability
- Clean separation of concerns
- Abstract interface for easy testing
- Comprehensive test coverage

### 4. Performance
- Connection pooling and reuse
- Efficient SPARQL query execution
- Concurrent operation support

## Testing

### Unit Tests
- Interface and implementation testing
- Mock-based testing for isolation
- Error condition testing

### Integration Tests
- Real GraphDB instance testing
- Backward compatibility validation
- Performance testing

### Test Results
All existing integration tests pass without modification, confirming backward compatibility.

## Configuration Migration

### Before (GraphDB-specific):
```yaml
graphdb:
  url: "http://localhost:7200"
  default_repository: "test"
```

### After (Generic):
```yaml
graphdb:
  url: "http://localhost:7200"
  default_repository: "PortfolioExample"
  store_type: "graphdb"  # Optional: graphdb, fuseki, stardog
```

## Usage Examples

### Basic Query
```python
# Same API as before - no changes needed
result = await client.execute_sparql_query("SELECT ?s ?p ?o WHERE { ?s ?p ?o } LIMIT 10")
```

### TTL Upload
```python
ttl_data = "@prefix ex: <http://example.org/> . ex:subject ex:predicate ex:object ."
success = await client.upload_ttl_data(ttl_data)
```

### Different Triple Store
```python
# Switch to Fuseki by changing configuration
settings = GraphDBSettings(
    url="http://localhost:3030",
    default_repository="dataset",
    store_type="fuseki"
)
```

## Future Enhancements

1. **Additional Store Support**: Add support for more triple stores (Blazegraph, Virtuoso, etc.)
2. **Advanced Features**: Implement SPARQL 1.1 features like federated queries
3. **Performance Optimization**: Add query caching and connection pooling
4. **Monitoring**: Add metrics and health monitoring capabilities

## Conclusion

The migration successfully replaces the GraphDB-specific implementation with a generic, standards-based solution while maintaining full backward compatibility. The system is now more portable, reliable, and maintainable.
