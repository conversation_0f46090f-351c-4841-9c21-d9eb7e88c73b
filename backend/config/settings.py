"""
Central configuration management using pydantic-settings.
Loads configuration from config.yaml and .env files.
Provides type-safe access to all application settings.
"""
from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import Field
from typing import Dict, Any, Optional


class KafkaSettings(BaseSettings):
    bootstrap_servers: str = Field(default="localhost:9092")
    group_id: str = Field(default="rdf-agent-consumer")
    usecase_topic_pattern: str = Field(default="data.usecases.*")


class GraphDBSettings(BaseSettings):
    """
    Triple store settings - supports GraphDB and other SPARQL-compatible stores.
    """
    url: str = Field(default="http://localhost:7200", description="Base URL of the triple store")
    default_repository: str = Field(default="PortfolioExample", description="Default repository/database name")
    username: Optional[str] = Field(default=None, description="Username for authentication")
    password: Optional[str] = Field(default=None, description="Password for authentication")
    timeout: int = Field(default=30, description="Request timeout in seconds")

    # Advanced settings for different triple store types
    store_type: str = Field(default="graphdb", description="Type of triple store (graphdb, fuseki, stardog, etc.)")
    query_endpoint_path: Optional[str] = Field(default=None, description="Custom query endpoint path")
    update_endpoint_path: Optional[str] = Field(default=None, description="Custom update endpoint path")

    @property
    def query_endpoint(self) -> str:
        """Build the query endpoint URL based on store type."""
        if self.query_endpoint_path:
            return f"{self.url.rstrip('/')}/{self.query_endpoint_path.lstrip('/')}"

        # Default patterns for different store types
        if self.store_type.lower() == "graphdb":
            return f"{self.url.rstrip('/')}/repositories/{self.default_repository}"
        elif self.store_type.lower() == "fuseki":
            return f"{self.url.rstrip('/')}/{self.default_repository}/sparql"
        elif self.store_type.lower() == "stardog":
            return f"{self.url.rstrip('/')}/{self.default_repository}/query"
        else:
            # Generic SPARQL endpoint
            return f"{self.url.rstrip('/')}/sparql"

    @property
    def update_endpoint(self) -> str:
        """Build the update endpoint URL based on store type."""
        if self.update_endpoint_path:
            return f"{self.url.rstrip('/')}/{self.update_endpoint_path.lstrip('/')}"

        # Default patterns for different store types
        if self.store_type.lower() == "graphdb":
            return f"{self.url.rstrip('/')}/repositories/{self.default_repository}/statements"
        elif self.store_type.lower() == "fuseki":
            return f"{self.url.rstrip('/')}/{self.default_repository}/update"
        elif self.store_type.lower() == "stardog":
            return f"{self.url.rstrip('/')}/{self.default_repository}/update"
        else:
            # Generic SPARQL update endpoint
            return f"{self.url.rstrip('/')}/update"


class MinIOSettings(BaseSettings):
    endpoint: str = Field(default="localhost:9000")
    access_key: str = Field(default="minioadmin")
    secret_key: str = Field(default="minioadmin")
    bucket_name: str = Field(default="data")


class QdrantSettings(BaseSettings):
    host: str = Field(default="localhost")
    port: int = Field(default=6333)
    collection_name: str = Field(default="documents")


class DoclingSettings(BaseSettings):
    """Configuration for Docling document extraction."""
    enabled: bool = Field(default=True)
    output_format: str = Field(default="markdown")  # Can be "markdown", "json", "text"
    timeout: int = Field(default=300)  # Timeout in seconds for document processing


class DatabaseSettings(BaseSettings):
    url: str = Field(default="sqlite:///./app.db")


class AISettings(BaseSettings):
    model: str = Field(default="openai:gpt-4.1-mini")
    api_key: str = Field(default="")
    or_api_key: str = Field(default="")  # OpenRouter API key


class Settings(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        env_nested_delimiter="__",
        extra="ignore"  # Ignore extra fields from .env
    )
    
    kafka: KafkaSettings = KafkaSettings()
    graphdb: GraphDBSettings = GraphDBSettings()
    minio: MinIOSettings = MinIOSettings()
    qdrant: QdrantSettings = QdrantSettings()
    docling: DoclingSettings = DoclingSettings()
    database: DatabaseSettings = DatabaseSettings()
    ai: AISettings = AISettings()
