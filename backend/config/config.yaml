# Application configuration
app:
  name: "RDF Agent System"
  version: "0.1.0"
  debug: false

# Kafka configuration
kafka:
  bootstrap_servers: "kafka:9092"
  group_id: "rdf-agent-consumer"
  usecase_topic_pattern: "data.usecases.*"

# GraphDB configuration
graphdb:
  url: "http://graphdb:7200"
  default_repository: "rdf_agent_db"

# MinIO configuration
minio:
  endpoint: "minio:9000"
  bucket_name: "data"

# Qdrant configuration
qdrant:
  host: "qdrant"
  port: 6333

# Docling configuration
docling:
  enabled: true
  output_format: "markdown"
  timeout: 300

# Database configuration
database:
  url: "sqlite:///./app.db"

# AI configuration
ai:
  model: "openai:gpt-4.1-mini"
