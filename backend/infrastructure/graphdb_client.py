"""
GraphDB client for RDF data storage and SPARQL queries.
Handles connection to GraphDB and provides query interface.
"""
import logging
from typing import Dict, List, Optional, Any
import aiohttp
from urllib.parse import urljoin

from config.settings import GraphDBSettings


logger = logging.getLogger(__name__)


class GraphDBClient:
    """Async client for GraphDB operations."""
    
    def __init__(self, settings: GraphDBSettings):
        self.settings = settings
        self.session: Optional[aiohttp.ClientSession] = None
        self.base_url = settings.url
        self.repository = settings.default_repository

    async def initialize(self):
        """Initialize the GraphDB client."""
        self.session = aiohttp.ClientSession()
        
        # Test connection
        try:
            await self.health_check()
            logger.info("GraphDB client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize GraphDB client: {e}")
            raise

    async def close(self):
        """Close the GraphDB client."""
        if self.session:
            await self.session.close()
        logger.info("GraphDB client closed")

    async def health_check(self) -> bool:
        """Check if GraphDB is accessible."""
        try:
            url = urljoin(self.base_url, "/rest/repositories")
            async with self.session.get(url) as response:
                return response.status == 200
        except Exception as e:
            logger.error(f"GraphDB health check failed: {e}")
            return False

    async def execute_sparql_query(self, query: str, repository: Optional[str] = None) -> Dict[str, Any]:
        """Execute a SPARQL query and return results."""
        repo = repository or self.repository
        url = urljoin(self.base_url, f"/repositories/{repo}")
        
        headers = {
            'Accept': 'application/sparql-results+json',
            'Content-Type': 'application/sparql-query'
        }
        
        try:
            async with self.session.post(url, data=query, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    logger.debug(f"SPARQL query executed successfully: {len(result.get('results', {}).get('bindings', []))} results")
                    return result
                else:
                    error_text = await response.text()
                    logger.error(f"SPARQL query failed with status {response.status}: {error_text}")
                    raise Exception(f"SPARQL query failed: {error_text}")
                    
        except Exception as e:
            logger.error(f"Error executing SPARQL query: {e}")
            raise

    async def execute_sparql_update(self, update: str, repository: Optional[str] = None) -> bool:
        """Execute a SPARQL update operation."""
        repo = repository or self.repository
        url = urljoin(self.base_url, f"/repositories/{repo}/statements")
        
        headers = {
            'Content-Type': 'application/sparql-update'
        }
        
        try:
            async with self.session.post(url, data=update, headers=headers) as response:
                if response.status in [200, 204]:
                    logger.debug("SPARQL update executed successfully")
                    return True
                else:
                    error_text = await response.text()
                    logger.error(f"SPARQL update failed with status {response.status}: {error_text}")
                    return False
                    
        except Exception as e:
            logger.error(f"Error executing SPARQL update: {e}")
            return False

    async def upload_ttl_data(self, ttl_content: str, repository: Optional[str] = None, 
                             context: Optional[str] = None) -> bool:
        """Upload TTL data to GraphDB."""
        repo = repository or self.repository
        url = urljoin(self.base_url, f"/repositories/{repo}/statements")
        
        headers = {
            'Content-Type': 'text/turtle'
        }
        
        params = {}
        if context:
            params['context'] = context
        
        try:
            async with self.session.post(url, data=ttl_content, headers=headers, params=params) as response:
                if response.status in [200, 204]:
                    logger.info("TTL data uploaded successfully")
                    return True
                else:
                    error_text = await response.text()
                    logger.error(f"TTL upload failed with status {response.status}: {error_text}")
                    return False
                    
        except Exception as e:
            logger.error(f"Error uploading TTL data: {e}")
            return False

    async def get_repositories(self) -> List[Dict[str, Any]]:
        """Get list of available repositories."""
        url = urljoin(self.base_url, "/rest/repositories")
        
        try:
            async with self.session.get(url) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    logger.error(f"Failed to get repositories: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"Error getting repositories: {e}")
            return []

    async def clear_repository(self, repository: Optional[str] = None) -> bool:
        """Clear all data from a repository."""
        repo = repository or self.repository
        url = urljoin(self.base_url, f"/repositories/{repo}/statements")
        
        try:
            async with self.session.delete(url) as response:
                if response.status in [200, 204]:
                    logger.info(f"Repository {repo} cleared successfully")
                    return True
                else:
                    logger.error(f"Failed to clear repository {repo}: {response.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"Error clearing repository {repo}: {e}")
            return False

    async def get_repository_size(self, repository: Optional[str] = None) -> int:
        """Get the number of statements in a repository."""
        repo = repository or self.repository
        url = urljoin(self.base_url, f"/repositories/{repo}/size")
        
        try:
            async with self.session.get(url) as response:
                if response.status == 200:
                    size_text = await response.text()
                    return int(size_text.strip())
                else:
                    logger.error(f"Failed to get repository size: {response.status}")
                    return 0
                    
        except Exception as e:
            logger.error(f"Error getting repository size: {e}")
            return 0
