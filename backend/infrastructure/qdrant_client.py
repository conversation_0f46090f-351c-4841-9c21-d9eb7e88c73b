"""
Qdrant client for vector database operations.
Handles document embeddings and semantic search.
"""
import logging
from typing import List, Dict, Any, Optional
import asyncio
from datetime import datetime
from qdrant_client import QdrantClient as QdrantSyncClient
from qdrant_client.http import models
from qdrant_client.http.models import Distance, VectorParams, PointStruct
from llama_index.core.node_parser import MarkdownNodeParser
from llama_index.core import Document

from config.settings import QdrantSettings


logger = logging.getLogger(__name__)


class QdrantClient:
    """Async wrapper for Qdrant operations."""

    def __init__(self, settings: QdrantSettings):
        self.settings = settings
        self.client: Optional[QdrantSyncClient] = None
        self.default_collection_name = settings.collection_name

    async def initialize(self):
        """Initialize the Qdrant client."""
        try:
            self.client = QdrantSyncClient(
                host=self.settings.host,
                port=self.settings.port
            )
            
            # Ensure collection exists
            await self._ensure_collection_exists()
            
            logger.info("Qdrant client initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Qdrant client: {e}")
            raise

    async def close(self):
        """Close the Qdrant client."""
        if self.client:
            self.client.close()
        logger.info("Qdrant client closed")

    async def _ensure_collection_exists(self, collection_name: Optional[str] = None):
        """Ensure the collection exists, create if it doesn't."""
        try:
            loop = asyncio.get_event_loop()
            target_collection = collection_name or self.default_collection_name

            # Check if collection exists
            collections = await loop.run_in_executor(
                None, self.client.get_collections
            )

            collection_names = [col.name for col in collections.collections]

            if target_collection not in collection_names:
                # Create collection with default vector configuration
                await loop.run_in_executor(
                    None,
                    self.client.create_collection,
                    target_collection,
                    VectorParams(size=384, distance=Distance.COSINE)  # Default for sentence transformers
                )
                logger.info(f"Created collection: {target_collection}")
            else:
                logger.info(f"Collection exists: {target_collection}")

        except Exception as e:
            logger.error(f"Error ensuring collection exists: {e}")
            raise

    async def create_usecase_collection(self, usecase_name: str) -> str:
        """Create a collection for a specific usecase."""
        # Normalize collection name (lowercase, replace spaces/special chars with underscores)
        collection_name = usecase_name.lower().replace(' ', '_').replace('-', '_')
        # Remove any non-alphanumeric characters except underscores
        collection_name = ''.join(c for c in collection_name if c.isalnum() or c == '_')

        await self._ensure_collection_exists(collection_name)
        return collection_name

    async def upsert_points(self, points: List[PointStruct], collection_name: Optional[str] = None) -> bool:
        """Upsert points into the collection."""
        try:
            loop = asyncio.get_event_loop()
            target_collection = collection_name or self.default_collection_name

            await loop.run_in_executor(
                None,
                self.client.upsert,
                target_collection,
                points
            )

            logger.info(f"Upserted {len(points)} points to collection {target_collection}")
            return True

        except Exception as e:
            logger.error(f"Error upserting points: {e}")
            return False

    def _chunk_markdown_text(self, text: str) -> List[Dict[str, Any]]:
        """Split markdown text using LlamaIndex MarkdownNodeParser for intelligent chunking."""
        try:
            # Create a LlamaIndex Document from the text
            document = Document(text=text)

            # Initialize the MarkdownNodeParser
            parser = MarkdownNodeParser()

            # Parse the document into nodes
            nodes = parser.get_nodes_from_documents([document])

            # Convert nodes to chunks with metadata
            chunks = []
            for i, node in enumerate(nodes):
                chunk_data = {
                    "text": node.text,
                    "metadata": {
                        "node_id": node.node_id,
                        "chunk_index": i,
                        "total_chunks": len(nodes),
                        **node.metadata  # Include any metadata from the node
                    }
                }
                chunks.append(chunk_data)

            logger.info(f"Successfully chunked markdown into {len(chunks)} intelligent chunks")
            return chunks

        except Exception as e:
            logger.error(f"Error chunking markdown with LlamaIndex: {e}")
            # Fallback to simple text splitting if LlamaIndex fails
            return self._fallback_chunk_text(text)

    def _fallback_chunk_text(self, text: str, chunk_size: int = 1000, overlap: int = 200) -> List[Dict[str, Any]]:
        """Fallback text chunking method if LlamaIndex fails."""
        if len(text) <= chunk_size:
            return [{"text": text, "metadata": {"chunk_index": 0, "total_chunks": 1}}]

        chunks = []
        start = 0
        chunk_index = 0

        while start < len(text):
            end = start + chunk_size

            # If this isn't the last chunk, try to break at a sentence or word boundary
            if end < len(text):
                # Look for sentence boundary (. ! ?)
                sentence_end = max(
                    text.rfind('.', start, end),
                    text.rfind('!', start, end),
                    text.rfind('?', start, end)
                )

                if sentence_end > start:
                    end = sentence_end + 1
                else:
                    # Look for word boundary
                    word_end = text.rfind(' ', start, end)
                    if word_end > start:
                        end = word_end

            chunk_text = text[start:end].strip()
            if chunk_text:
                chunks.append({
                    "text": chunk_text,
                    "metadata": {
                        "chunk_index": chunk_index,
                        "total_chunks": 0  # Will be updated after all chunks are created
                    }
                })
                chunk_index += 1

            # Move start position with overlap
            start = end - overlap if end < len(text) else end

        # Update total_chunks for all chunks
        for chunk in chunks:
            chunk["metadata"]["total_chunks"] = len(chunks)

        return chunks

    async def upsert_document_chunks(self,
                                   collection_name: str,
                                   document_content: str,
                                   document_metadata: Dict[str, Any],
                                   embedding_function,
                                   chunk_size: int = 1000,
                                   overlap: int = 200) -> bool:
        """Chunk a document and upsert all chunks to the collection using intelligent markdown parsing."""
        try:
            # Ensure collection exists
            await self._ensure_collection_exists(collection_name)

            # Chunk the document using LlamaIndex MarkdownNodeParser
            chunks = self._chunk_markdown_text(document_content)

            # Create points for each chunk
            points = []
            for chunk_data in chunks:
                chunk_text = chunk_data["text"]
                chunk_metadata = chunk_data["metadata"]

                # Generate embedding for the chunk
                embedding = await embedding_function(chunk_text)

                # Create a unique point ID using UUID
                import uuid
                import hashlib
                # Create deterministic UUID from document path and chunk index
                chunk_index = chunk_metadata.get("chunk_index", 0)
                seed_string = f"{document_metadata.get('document_path', '')}_chunk_{chunk_index}"
                seed_hash = hashlib.md5(seed_string.encode()).hexdigest()
                point_id = str(uuid.UUID(seed_hash))

                # Create point with properly structured metadata following Qdrant best practices
                point = PointStruct(
                    id=point_id,
                    vector=embedding,
                    payload={
                        # Main content for search
                        "content": chunk_text,

                        # Chunk information
                        "chunk": {
                            "index": chunk_metadata.get("chunk_index", 0),
                            "total": chunk_metadata.get("total_chunks", len(chunks)),
                            "node_id": chunk_metadata.get("node_id", ""),
                            "method": "llamaindex_markdown_parser"
                        },

                        # Document information - structured metadata
                        "document": document_metadata.get("document", {
                            "type": document_metadata.get("type", "document"),
                            "original_path": document_metadata.get("document_path", ""),
                            "file_paths": document_metadata.get("file_paths", {}),
                            "building_id": document_metadata.get("building_id", "")
                        }),

                        # Usecase information
                        "usecase": document_metadata.get("usecase", {
                            "name": document_metadata.get("usecase", ""),
                            "collection": collection_name
                        }),

                        # Processing information
                        "processing": {
                            **document_metadata.get("processing", {"method": "unknown"}),
                            "indexed_at": datetime.utcnow().isoformat()
                        },

                        # Add indexed_at at root level for easy filtering
                        "indexed_at": datetime.utcnow().isoformat(),

                        # Include any additional LlamaIndex node metadata
                        "llamaindex": {k: v for k, v in chunk_metadata.items()
                                     if k not in ["chunk_index", "total_chunks", "node_id"]}
                    }
                )
                points.append(point)

            # Upsert all chunks
            success = await self.upsert_points(points, collection_name)

            if success:
                logger.info(f"Successfully upserted {len(chunks)} intelligent markdown chunks for document to collection {collection_name}")

            return success

        except Exception as e:
            logger.error(f"Error upserting document chunks: {e}")
            return False

    async def search(self, query_vector: List[float], limit: int = 10,
                    score_threshold: Optional[float] = None,
                    filter_conditions: Optional[Dict[str, Any]] = None,
                    collection_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """Search for similar vectors."""
        try:
            loop = asyncio.get_event_loop()
            target_collection = collection_name or self.default_collection_name

            search_params = {
                "collection_name": target_collection,
                "query": query_vector,
                "limit": limit
            }

            if score_threshold is not None:
                search_params["score_threshold"] = score_threshold

            if filter_conditions:
                search_params["query_filter"] = models.Filter(**filter_conditions)

            results = await loop.run_in_executor(
                None,
                lambda: self.client.query_points(**search_params)
            )
            
            # Convert results to dict format
            search_results = []
            for result in results.points:
                search_results.append({
                    "id": result.id,
                    "score": result.score,
                    "payload": result.payload,
                    "vector": result.vector
                })
            
            logger.info(f"Found {len(search_results)} similar vectors")
            return search_results
            
        except Exception as e:
            logger.error(f"Error searching vectors: {e}")
            return []

    async def get_point(self, point_id: str, collection_name: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Get a specific point by ID."""
        try:
            loop = asyncio.get_event_loop()
            target_collection = collection_name or self.default_collection_name

            result = await loop.run_in_executor(
                None,
                self.client.retrieve,
                target_collection,
                [point_id]
            )
            
            if result:
                point = result[0]
                return {
                    "id": point.id,
                    "payload": point.payload,
                    "vector": point.vector
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting point {point_id}: {e}")
            return None

    async def delete_points(self, point_ids: List[str], collection_name: Optional[str] = None) -> bool:
        """Delete points by IDs."""
        try:
            loop = asyncio.get_event_loop()
            target_collection = collection_name or self.default_collection_name

            await loop.run_in_executor(
                None,
                self.client.delete,
                target_collection,
                point_ids
            )

            logger.info(f"Deleted {len(point_ids)} points from collection {target_collection}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting points: {e}")
            return False

    async def count_points(self, collection_name: Optional[str] = None) -> int:
        """Count total points in the collection."""
        try:
            loop = asyncio.get_event_loop()
            target_collection = collection_name or self.default_collection_name

            info = await loop.run_in_executor(
                None,
                self.client.get_collection,
                target_collection
            )

            return info.points_count
            
        except Exception as e:
            logger.error(f"Error counting points: {e}")
            return 0

    async def create_index(self, field_name: str, field_type: str = "keyword") -> bool:
        """Create an index on a payload field."""
        try:
            loop = asyncio.get_event_loop()
            
            await loop.run_in_executor(
                None,
                self.client.create_payload_index,
                self.collection_name,
                field_name,
                field_type
            )
            
            logger.info(f"Created index on field {field_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error creating index on field {field_name}: {e}")
            return False

    async def scroll_points(self, limit: int = 100, offset: Optional[str] = None) -> Dict[str, Any]:
        """Scroll through points in the collection."""
        try:
            loop = asyncio.get_event_loop()
            
            result = await loop.run_in_executor(
                None,
                self.client.scroll,
                self.collection_name,
                limit,
                offset
            )
            
            points = []
            for point in result[0]:
                points.append({
                    "id": point.id,
                    "payload": point.payload,
                    "vector": point.vector
                })
            
            return {
                "points": points,
                "next_page_offset": result[1]
            }
            
        except Exception as e:
            logger.error(f"Error scrolling points: {e}")
            return {"points": [], "next_page_offset": None}
