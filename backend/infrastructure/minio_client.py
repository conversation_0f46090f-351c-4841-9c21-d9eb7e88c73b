"""
MinIO client for object storage operations.
Handles document storage and retrieval from MinIO.
"""
import logging
from typing import Optional, List, Dict, Any, AsyncIterator
import asyncio
from pathlib import Path
import aiofiles
from minio import Minio
from minio.error import S3Error

from config.settings import MinIOSettings
from models.data_models import DocumentInfo


logger = logging.getLogger(__name__)


class MinIOClient:
    """Async wrapper for MinIO operations."""
    
    def __init__(self, settings: MinIOSettings):
        self.settings = settings
        self.client: Optional[Minio] = None
        self.bucket_name = settings.bucket_name

    async def initialize(self):
        """Initialize the MinIO client."""
        try:
            self.client = Minio(
                endpoint=self.settings.endpoint,
                access_key=self.settings.access_key,
                secret_key=self.settings.secret_key,
                secure=False  # Set to True for HTTPS
            )
            
            # Ensure bucket exists
            await self._ensure_bucket_exists()
            
            logger.info("MinIO client initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize MinIO client: {e}")
            raise

    async def close(self):
        """Close the MinIO client."""
        # MinIO client doesn't need explicit closing
        logger.info("MinIO client closed")

    async def _ensure_bucket_exists(self):
        """Ensure the bucket exists, create if it doesn't."""
        try:
            # Run in thread pool since minio client is sync
            loop = asyncio.get_event_loop()
            bucket_exists = await loop.run_in_executor(
                None, self.client.bucket_exists, self.bucket_name
            )
            
            if not bucket_exists:
                await loop.run_in_executor(
                    None, self.client.make_bucket, self.bucket_name
                )
                logger.info(f"Created bucket: {self.bucket_name}")
            else:
                logger.info(f"Bucket exists: {self.bucket_name}")
                
        except Exception as e:
            logger.error(f"Error ensuring bucket exists: {e}")
            raise

    async def upload_file(self, file_path: str, object_name: str, 
                         metadata: Optional[Dict[str, str]] = None) -> bool:
        """Upload a file to MinIO."""
        try:
            loop = asyncio.get_event_loop()
            
            # Get file size
            file_size = Path(file_path).stat().st_size
            
            # Upload file
            await loop.run_in_executor(
                None,
                self.client.fput_object,
                self.bucket_name,
                object_name,
                file_path,
                metadata or {}
            )
            
            logger.info(f"Uploaded file {file_path} as {object_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error uploading file {file_path}: {e}")
            return False

    async def upload_data(self, data: bytes, object_name: str, 
                         content_type: str = "application/octet-stream",
                         metadata: Optional[Dict[str, str]] = None) -> bool:
        """Upload data to MinIO."""
        try:
            from io import BytesIO
            
            loop = asyncio.get_event_loop()
            data_stream = BytesIO(data)
            
            await loop.run_in_executor(
                None,
                self.client.put_object,
                self.bucket_name,
                object_name,
                data_stream,
                len(data),
                content_type,
                metadata or {}
            )
            
            logger.info(f"Uploaded data as {object_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error uploading data as {object_name}: {e}")
            return False

    async def download_file(self, object_name: str, file_path: str) -> bool:
        """Download a file from MinIO."""
        try:
            loop = asyncio.get_event_loop()
            
            await loop.run_in_executor(
                None,
                self.client.fget_object,
                self.bucket_name,
                object_name,
                file_path
            )
            
            logger.info(f"Downloaded {object_name} to {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error downloading {object_name}: {e}")
            return False

    async def download_data(self, object_name: str) -> Optional[bytes]:
        """Download data from MinIO."""
        try:
            loop = asyncio.get_event_loop()
            
            response = await loop.run_in_executor(
                None,
                self.client.get_object,
                self.bucket_name,
                object_name
            )
            
            data = response.read()
            response.close()
            response.release_conn()
            
            logger.info(f"Downloaded data from {object_name}")
            return data
            
        except Exception as e:
            logger.error(f"Error downloading data from {object_name}: {e}")
            return None

    async def list_objects(self, prefix: str = "", recursive: bool = True) -> List[str]:
        """List objects in the bucket."""
        try:
            loop = asyncio.get_event_loop()
            
            objects = await loop.run_in_executor(
                None,
                lambda: list(self.client.list_objects(
                    self.bucket_name, prefix=prefix, recursive=recursive
                ))
            )
            
            return [obj.object_name for obj in objects]
            
        except Exception as e:
            logger.error(f"Error listing objects: {e}")
            return []

    async def object_exists(self, object_name: str) -> bool:
        """Check if an object exists."""
        try:
            loop = asyncio.get_event_loop()
            
            await loop.run_in_executor(
                None,
                self.client.stat_object,
                self.bucket_name,
                object_name
            )
            
            return True
            
        except S3Error as e:
            if e.code == "NoSuchKey":
                return False
            logger.error(f"Error checking object existence: {e}")
            return False
        except Exception as e:
            logger.error(f"Error checking object existence: {e}")
            return False

    async def delete_object(self, object_name: str) -> bool:
        """Delete an object from MinIO."""
        try:
            loop = asyncio.get_event_loop()
            
            await loop.run_in_executor(
                None,
                self.client.remove_object,
                self.bucket_name,
                object_name
            )
            
            logger.info(f"Deleted object {object_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting object {object_name}: {e}")
            return False

    async def get_object_info(self, object_name: str) -> Optional[Dict[str, Any]]:
        """Get object information."""
        try:
            loop = asyncio.get_event_loop()

            stat = await loop.run_in_executor(
                None,
                self.client.stat_object,
                self.bucket_name,
                object_name
            )

            return {
                "object_name": stat.object_name,
                "size": stat.size,
                "etag": stat.etag,
                "last_modified": stat.last_modified,
                "content_type": stat.content_type,
                "metadata": stat.metadata
            }

        except Exception as e:
            logger.error(f"Error getting object info for {object_name}: {e}")
            return None

    async def put_object(self, object_name: str, data: bytes,
                        content_type: str = "application/octet-stream") -> bool:
        """Put object data to MinIO (alias for upload_data)."""
        return await self.upload_data(data, object_name, content_type)

    async def health_check(self) -> bool:
        """Check if MinIO is healthy by listing buckets."""
        try:
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, self.client.list_buckets)
            return True
        except Exception as e:
            logger.error(f"MinIO health check failed: {e}")
            return False
