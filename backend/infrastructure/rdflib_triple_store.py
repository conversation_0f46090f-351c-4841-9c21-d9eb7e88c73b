"""
RDFLib-based implementation of the triple store interface.
Uses R<PERSON><PERSON>ib and SPARQLWrapper for generic SPARQL operations.
"""
import logging
import asyncio
from typing import Dict, List, Optional, Any
from urllib.parse import urljoin

import aiohttp
from rdflib import Graph
from rdflib.plugins.stores.sparqlstore import SPARQLS<PERSON>
from SPARQLWrapper import SPAR<PERSON><PERSON>rapper, JSON, XML, POST, GET
from SPARQLWrapper.SPARQLExceptions import SPARQLW<PERSON><PERSON><PERSON>xception

from .triple_store_interface import (
    TripleStoreInterface,
    TripleStoreConfig,
    SPARQLResult,
    SPARQLUpdateResult,
    TripleStoreError,
    TripleStoreConnectionError,
    TripleStoreQueryError,
    TripleStoreUpdateError
)

logger = logging.getLogger(__name__)


class RDFLibTripleStore(TripleStoreInterface):
    """RDFLib-based implementation for SPARQL triple stores."""
    
    def __init__(self, config: TripleStoreConfig):
        super().__init__(config)
        self.session: Optional[aiohttp.ClientSession] = None
        self._query_wrapper: Optional[SPARQLWrapper] = None
        self._update_wrapper: Optional[SPARQLWrapper] = None
    
    async def initialize(self) -> None:
        """Initialize the RDFLib triple store client."""
        try:
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=self.config.timeout)
            )
            
            # Initialize SPARQLWrapper for queries
            query_url = self._build_query_endpoint_url()
            self._query_wrapper = SPARQLWrapper(query_url)
            self._query_wrapper.setReturnFormat(JSON)
            
            # Initialize SPARQLWrapper for updates if endpoint is available
            if self.config.has_update_endpoint:
                update_url = self._build_update_endpoint_url()
                self._update_wrapper = SPARQLWrapper(update_url)
                self._update_wrapper.setMethod(POST)
            
            # Set authentication if provided
            if self.config.username and self.config.password:
                if self._query_wrapper:
                    self._query_wrapper.setCredentials(self.config.username, self.config.password)
                if self._update_wrapper:
                    self._update_wrapper.setCredentials(self.config.username, self.config.password)
            
            # Test connection
            await self.health_check()
            self.logger.info("RDFLib triple store client initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize RDFLib triple store client: {e}")
            raise TripleStoreConnectionError(f"Failed to initialize: {e}")
    
    async def close(self) -> None:
        """Close the triple store client."""
        if self.session:
            await self.session.close()
        self.logger.info("RDFLib triple store client closed")
    
    async def health_check(self) -> bool:
        """Check if the triple store is accessible."""
        try:
            # Simple ASK query to test connectivity
            test_query = "ASK { ?s ?p ?o }"
            result = await self.execute_sparql_query(test_query)
            return result.success
        except Exception as e:
            self.logger.error(f"Health check failed: {e}")
            return False
    
    async def execute_sparql_query(
        self, 
        query: str, 
        repository: Optional[str] = None
    ) -> SPARQLResult:
        """Execute a SPARQL query using SPARQLWrapper."""
        try:
            # Update endpoint URL if repository is specified
            if repository:
                query_url = self._build_query_endpoint_url(repository)
                wrapper = SPARQLWrapper(query_url)
                wrapper.setReturnFormat(JSON)
                if self.config.username and self.config.password:
                    wrapper.setCredentials(self.config.username, self.config.password)
            else:
                wrapper = self._query_wrapper
            
            if not wrapper:
                raise TripleStoreQueryError("Query wrapper not initialized")
            
            wrapper.setQuery(query)
            
            # Execute query in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            raw_result = await loop.run_in_executor(None, wrapper.queryAndConvert)
            
            # Parse results based on query type
            if query.strip().upper().startswith('ASK'):
                # ASK query returns boolean
                return SPARQLResult(
                    success=True,
                    results=[{"result": raw_result}],
                    variables=["result"],
                    count=1,
                    raw_response=raw_result
                )
            elif query.strip().upper().startswith(('CONSTRUCT', 'DESCRIBE')):
                # CONSTRUCT/DESCRIBE queries return RDF graph
                # Convert to triples for consistency
                results = []
                if hasattr(raw_result, '__iter__'):
                    for triple in raw_result:
                        results.append({
                            "subject": str(triple[0]),
                            "predicate": str(triple[1]),
                            "object": str(triple[2])
                        })
                
                return SPARQLResult(
                    success=True,
                    results=results,
                    variables=["subject", "predicate", "object"],
                    count=len(results),
                    raw_response=raw_result
                )
            else:
                # SELECT query returns bindings
                bindings = raw_result.get('results', {}).get('bindings', [])
                variables = raw_result.get('head', {}).get('vars', [])
                
                # Convert bindings to simplified format
                results = []
                for binding in bindings:
                    result = {}
                    for var in variables:
                        if var in binding:
                            result[var] = binding[var].get('value', '')
                        else:
                            result[var] = None
                    results.append(result)
                
                return SPARQLResult(
                    success=True,
                    results=results,
                    variables=variables,
                    count=len(results),
                    raw_response=raw_result
                )
                
        except SPARQLWrapperException as e:
            error_msg = f"SPARQL query failed: {e}"
            self.logger.error(error_msg)
            return SPARQLResult(
                success=False,
                results=[],
                variables=[],
                count=0,
                error_message=error_msg
            )
        except Exception as e:
            error_msg = f"Error executing SPARQL query: {e}"
            self.logger.error(error_msg)
            return SPARQLResult(
                success=False,
                results=[],
                variables=[],
                count=0,
                error_message=error_msg
            )
    
    async def execute_sparql_update(
        self, 
        update: str, 
        repository: Optional[str] = None
    ) -> SPARQLUpdateResult:
        """Execute a SPARQL update using SPARQLWrapper."""
        if not self.config.has_update_endpoint:
            return SPARQLUpdateResult(
                success=False,
                error_message="Update endpoint not configured"
            )
        
        try:
            # Update endpoint URL if repository is specified
            if repository:
                update_url = self._build_update_endpoint_url(repository)
                wrapper = SPARQLWrapper(update_url)
                wrapper.setMethod(POST)
                if self.config.username and self.config.password:
                    wrapper.setCredentials(self.config.username, self.config.password)
            else:
                wrapper = self._update_wrapper
            
            if not wrapper:
                raise TripleStoreUpdateError("Update wrapper not initialized")
            
            wrapper.setQuery(update)
            
            # Execute update in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, wrapper.queryAndConvert)
            
            return SPARQLUpdateResult(success=True)
            
        except SPARQLWrapperException as e:
            error_msg = f"SPARQL update failed: {e}"
            self.logger.error(error_msg)
            return SPARQLUpdateResult(success=False, error_message=error_msg)
        except Exception as e:
            error_msg = f"Error executing SPARQL update: {e}"
            self.logger.error(error_msg)
            return SPARQLUpdateResult(success=False, error_message=error_msg)

    async def upload_ttl_data(
        self,
        ttl_content: str,
        repository: Optional[str] = None,
        context: Optional[str] = None
    ) -> SPARQLUpdateResult:
        """Upload TTL data using SPARQL INSERT DATA."""
        try:
            # Parse TTL content to validate it
            temp_graph = Graph()
            temp_graph.parse(data=ttl_content, format='turtle')

            # Convert to SPARQL INSERT DATA query
            if context:
                insert_query = f"""
                INSERT DATA {{
                    GRAPH <{context}> {{
                        {ttl_content}
                    }}
                }}
                """
            else:
                insert_query = f"""
                INSERT DATA {{
                    {ttl_content}
                }}
                """

            result = await self.execute_sparql_update(insert_query, repository)
            if result.success:
                self.logger.info("TTL data uploaded successfully")

            return result

        except Exception as e:
            error_msg = f"Error uploading TTL data: {e}"
            self.logger.error(error_msg)
            return SPARQLUpdateResult(success=False, error_message=error_msg)

    async def get_repositories(self) -> List[Dict[str, Any]]:
        """Get list of available repositories using HTTP API."""
        try:
            # Try GraphDB-style repository listing first
            base_url = self.config.query_endpoint.split('/repositories/')[0] if '/repositories/' in self.config.query_endpoint else self.config.query_endpoint
            repos_url = urljoin(base_url.rstrip('/') + '/', 'rest/repositories')

            async with self.session.get(repos_url) as response:
                if response.status == 200:
                    repos_data = await response.json()
                    return repos_data if isinstance(repos_data, list) else []
                else:
                    self.logger.warning(f"Failed to get repositories via REST API: {response.status}")
                    return []

        except Exception as e:
            self.logger.error(f"Error getting repositories: {e}")
            return []

    async def clear_repository(self, repository: Optional[str] = None) -> SPARQLUpdateResult:
        """Clear all data from a repository using SPARQL DELETE."""
        try:
            clear_query = "DELETE WHERE { ?s ?p ?o }"
            result = await self.execute_sparql_update(clear_query, repository)

            if result.success:
                repo_name = self._get_repository(repository)
                self.logger.info(f"Repository {repo_name} cleared successfully")

            return result

        except Exception as e:
            error_msg = f"Error clearing repository: {e}"
            self.logger.error(error_msg)
            return SPARQLUpdateResult(success=False, error_message=error_msg)

    async def get_repository_size(self, repository: Optional[str] = None) -> int:
        """Get the number of statements in a repository using SPARQL COUNT."""
        try:
            count_query = "SELECT (COUNT(*) as ?count) WHERE { ?s ?p ?o }"
            result = await self.execute_sparql_query(count_query, repository)

            if result.success and result.results:
                count_str = result.results[0].get('count', '0')
                return int(count_str)
            else:
                self.logger.warning("Failed to get repository size")
                return 0

        except Exception as e:
            self.logger.error(f"Error getting repository size: {e}")
            return 0
