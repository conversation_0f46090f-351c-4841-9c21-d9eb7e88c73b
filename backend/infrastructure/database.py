"""
Database manager for SQLite operations.
Handles session storage and conversation history.
"""
import logging
import asyncio
from typing import Optional, List, Dict, Any
from datetime import datetime
import aiosqlite
from pathlib import Path

from models.data_models import Session, ConversationTurn, ProcessingTask


logger = logging.getLogger(__name__)


class DatabaseManager:
    """Async database manager for SQLite operations."""
    
    def __init__(self, database_url: str):
        self.database_url = database_url
        # Extract path from sqlite:///path format
        if database_url.startswith("sqlite:///"):
            self.db_path = database_url[10:]  # Remove sqlite:///
        else:
            self.db_path = database_url
        
        self.connection: Optional[aiosqlite.Connection] = None

    async def initialize(self):
        """Initialize the database and create tables."""
        try:
            # Ensure directory exists
            db_path = Path(self.db_path)
            db_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Create connection
            self.connection = await aiosqlite.connect(self.db_path)
            
            # Enable foreign keys
            await self.connection.execute("PRAGMA foreign_keys = ON")
            
            # Create tables
            await self._create_tables()
            
            logger.info(f"Database initialized: {self.db_path}")
            
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise

    async def close(self):
        """Close the database connection."""
        if self.connection:
            await self.connection.close()
        logger.info("Database connection closed")

    async def _create_tables(self):
        """Create database tables."""
        # Sessions table
        await self.connection.execute("""
            CREATE TABLE IF NOT EXISTS sessions (
                session_id TEXT PRIMARY KEY,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                metadata TEXT DEFAULT '{}'
            )
        """)
        
        # Conversation turns table
        await self.connection.execute("""
            CREATE TABLE IF NOT EXISTS conversation_turns (
                turn_id TEXT PRIMARY KEY,
                session_id TEXT NOT NULL,
                user_message TEXT NOT NULL,
                assistant_response TEXT NOT NULL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                metadata TEXT DEFAULT '{}',
                FOREIGN KEY (session_id) REFERENCES sessions (session_id)
            )
        """)
        
        # Processing tasks table
        await self.connection.execute("""
            CREATE TABLE IF NOT EXISTS processing_tasks (
                task_id TEXT PRIMARY KEY,
                document_id TEXT NOT NULL,
                filename TEXT NOT NULL,
                file_path TEXT NOT NULL,
                mime_type TEXT NOT NULL,
                size INTEGER NOT NULL,
                status TEXT NOT NULL DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                error_message TEXT,
                result TEXT,
                metadata TEXT DEFAULT '{}'
            )
        """)
        
        # Create indexes
        await self.connection.execute("""
            CREATE INDEX IF NOT EXISTS idx_turns_session_id 
            ON conversation_turns (session_id)
        """)
        
        await self.connection.execute("""
            CREATE INDEX IF NOT EXISTS idx_turns_timestamp 
            ON conversation_turns (timestamp)
        """)
        
        await self.connection.execute("""
            CREATE INDEX IF NOT EXISTS idx_tasks_status 
            ON processing_tasks (status)
        """)
        
        await self.connection.commit()

    async def create_session(self, session_id: str, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """Create a new session."""
        try:
            import json
            
            await self.connection.execute("""
                INSERT INTO sessions (session_id, metadata)
                VALUES (?, ?)
            """, (session_id, json.dumps(metadata or {})))
            
            await self.connection.commit()
            logger.info(f"Created session: {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error creating session {session_id}: {e}")
            return False

    async def get_session(self, session_id: str) -> Optional[Session]:
        """Get a session by ID."""
        try:
            import json
            
            cursor = await self.connection.execute("""
                SELECT session_id, created_at, updated_at, metadata
                FROM sessions WHERE session_id = ?
            """, (session_id,))
            
            row = await cursor.fetchone()
            if not row:
                return None
            
            # Get conversation turns
            turns_cursor = await self.connection.execute("""
                SELECT turn_id, user_message, assistant_response, timestamp, metadata
                FROM conversation_turns 
                WHERE session_id = ? 
                ORDER BY timestamp
            """, (session_id,))
            
            turns_rows = await turns_cursor.fetchall()
            turns = []
            
            for turn_row in turns_rows:
                turn = ConversationTurn(
                    turn_id=turn_row[0],
                    session_id=session_id,
                    user_message=turn_row[1],
                    assistant_response=turn_row[2],
                    timestamp=datetime.fromisoformat(turn_row[3]),
                    metadata=json.loads(turn_row[4])
                )
                turns.append(turn)
            
            session = Session(
                session_id=row[0],
                created_at=datetime.fromisoformat(row[1]),
                updated_at=datetime.fromisoformat(row[2]),
                metadata=json.loads(row[3]),
                turns=turns
            )
            
            return session
            
        except Exception as e:
            logger.error(f"Error getting session {session_id}: {e}")
            return None

    async def add_conversation_turn(self, turn: ConversationTurn) -> bool:
        """Add a conversation turn to a session."""
        try:
            import json
            
            await self.connection.execute("""
                INSERT INTO conversation_turns 
                (turn_id, session_id, user_message, assistant_response, timestamp, metadata)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                turn.turn_id,
                turn.session_id,
                turn.user_message,
                turn.assistant_response,
                turn.timestamp.isoformat(),
                json.dumps(turn.metadata)
            ))
            
            # Update session timestamp
            await self.connection.execute("""
                UPDATE sessions SET updated_at = CURRENT_TIMESTAMP
                WHERE session_id = ?
            """, (turn.session_id,))
            
            await self.connection.commit()
            logger.info(f"Added conversation turn: {turn.turn_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding conversation turn: {e}")
            return False

    async def create_processing_task(self, task: ProcessingTask) -> bool:
        """Create a processing task."""
        try:
            import json
            
            await self.connection.execute("""
                INSERT INTO processing_tasks 
                (task_id, document_id, filename, file_path, mime_type, size, 
                 status, created_at, updated_at, error_message, result, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                task.task_id,
                task.document_info.document_id,
                task.document_info.filename,
                task.document_info.file_path,
                task.document_info.mime_type,
                task.document_info.size,
                task.status.value,
                task.created_at.isoformat(),
                task.updated_at.isoformat(),
                task.error_message,
                json.dumps(task.result) if task.result else None,
                json.dumps(task.document_info.metadata)
            ))
            
            await self.connection.commit()
            logger.info(f"Created processing task: {task.task_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error creating processing task: {e}")
            return False

    async def update_processing_task(self, task_id: str, status: str, 
                                   error_message: Optional[str] = None,
                                   result: Optional[Dict[str, Any]] = None) -> bool:
        """Update a processing task."""
        try:
            import json
            
            await self.connection.execute("""
                UPDATE processing_tasks 
                SET status = ?, updated_at = CURRENT_TIMESTAMP, 
                    error_message = ?, result = ?
                WHERE task_id = ?
            """, (
                status,
                error_message,
                json.dumps(result) if result else None,
                task_id
            ))
            
            await self.connection.commit()
            logger.info(f"Updated processing task: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating processing task {task_id}: {e}")
            return False
