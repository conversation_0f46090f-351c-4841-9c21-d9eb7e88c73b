"""
Abstract interface for triple store operations.
Provides a generic interface that can work with any SPARQL-compatible triple store.
"""
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class TripleStoreConfig:
    """Configuration for triple store connection."""
    query_endpoint: str
    update_endpoint: Optional[str] = None
    default_repository: Optional[str] = None
    username: Optional[str] = None
    password: Optional[str] = None
    timeout: int = 30
    
    @property
    def has_update_endpoint(self) -> bool:
        """Check if update operations are supported."""
        return self.update_endpoint is not None


@dataclass
class SPARQLResult:
    """Standardized SPARQL query result."""
    success: bool
    results: List[Dict[str, Any]]
    variables: List[str]
    count: int
    error_message: Optional[str] = None
    raw_response: Optional[Dict[str, Any]] = None


@dataclass
class SPARQLUpdateResult:
    """Standardized SPARQL update result."""
    success: bool
    error_message: Optional[str] = None
    affected_triples: Optional[int] = None


class TripleStoreInterface(ABC):
    """Abstract interface for triple store operations."""
    
    def __init__(self, config: TripleStoreConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the triple store client."""
        pass
    
    @abstractmethod
    async def close(self) -> None:
        """Close the triple store client."""
        pass
    
    @abstractmethod
    async def health_check(self) -> bool:
        """Check if the triple store is accessible."""
        pass
    
    @abstractmethod
    async def execute_sparql_query(
        self, 
        query: str, 
        repository: Optional[str] = None
    ) -> SPARQLResult:
        """Execute a SPARQL query and return standardized results."""
        pass
    
    @abstractmethod
    async def execute_sparql_update(
        self, 
        update: str, 
        repository: Optional[str] = None
    ) -> SPARQLUpdateResult:
        """Execute a SPARQL update operation."""
        pass
    
    @abstractmethod
    async def upload_ttl_data(
        self, 
        ttl_content: str, 
        repository: Optional[str] = None,
        context: Optional[str] = None
    ) -> SPARQLUpdateResult:
        """Upload TTL data to the triple store."""
        pass
    
    @abstractmethod
    async def get_repositories(self) -> List[Dict[str, Any]]:
        """Get list of available repositories."""
        pass
    
    @abstractmethod
    async def clear_repository(self, repository: Optional[str] = None) -> SPARQLUpdateResult:
        """Clear all data from a repository."""
        pass
    
    @abstractmethod
    async def get_repository_size(self, repository: Optional[str] = None) -> int:
        """Get the number of statements in a repository."""
        pass
    
    # Helper methods that can be overridden by implementations
    def _get_repository(self, repository: Optional[str] = None) -> str:
        """Get the repository name to use."""
        return repository or self.config.default_repository or "default"
    
    def _build_query_endpoint_url(self, repository: Optional[str] = None) -> str:
        """Build the query endpoint URL for a specific repository."""
        repo = self._get_repository(repository)
        base_url = self.config.query_endpoint.rstrip('/')
        
        # Handle different endpoint patterns
        if '/repositories/' in base_url:
            # GraphDB style: http://localhost:7200/repositories/repo
            if not base_url.endswith(f'/repositories/{repo}'):
                return f"{base_url.split('/repositories/')[0]}/repositories/{repo}"
            return base_url
        else:
            # Generic SPARQL endpoint
            return base_url
    
    def _build_update_endpoint_url(self, repository: Optional[str] = None) -> str:
        """Build the update endpoint URL for a specific repository."""
        if not self.config.update_endpoint:
            raise ValueError("Update endpoint not configured")
        
        repo = self._get_repository(repository)
        base_url = self.config.update_endpoint.rstrip('/')
        
        # Handle different endpoint patterns
        if '/repositories/' in base_url:
            # GraphDB style: http://localhost:7200/repositories/repo/statements
            if not base_url.endswith(f'/repositories/{repo}/statements'):
                base_parts = base_url.split('/repositories/')[0]
                return f"{base_parts}/repositories/{repo}/statements"
            return base_url
        else:
            # Generic SPARQL update endpoint
            return base_url


class TripleStoreError(Exception):
    """Base exception for triple store operations."""
    pass


class TripleStoreConnectionError(TripleStoreError):
    """Exception raised when connection to triple store fails."""
    pass


class TripleStoreQueryError(TripleStoreError):
    """Exception raised when SPARQL query fails."""
    pass


class TripleStoreUpdateError(TripleStoreError):
    """Exception raised when SPARQL update fails."""
    pass
