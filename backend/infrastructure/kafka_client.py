"""
Kafka client for consuming and producing messages.
Handles building and address data from Kafka topics.
"""
import asyncio
import json
import logging
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime

from aiokafka import AIOKafkaConsumer, AIOKafkaProducer
from aiokafka.errors import KafkaError

from config.settings import KafkaSettings
from models.data_models import KafkaMessage, MessageType, BuildingData, AddressData


logger = logging.getLogger(__name__)


class KafkaClient:
    """Async Kafka client for consuming and producing messages."""
    
    def __init__(self, settings: KafkaSettings):
        self.settings = settings
        self.consumer: Optional[AIOKafkaConsumer] = None
        self.producer: Optional[AIOKafkaProducer] = None
        self.message_handlers: Dict[str, Callable] = {}
        self._running = False

    async def initialize(self):
        """Initialize Kafka consumer and producer."""
        try:
            # Initialize consumer
            self.consumer = AIOKafkaConsumer(
                bootstrap_servers=self.settings.bootstrap_servers,
                group_id=self.settings.group_id,
                value_deserializer=lambda m: json.loads(m.decode('utf-8')),
                auto_offset_reset='latest'
            )
            
            # Initialize producer
            self.producer = AIOKafkaProducer(
                bootstrap_servers=self.settings.bootstrap_servers,
                value_serializer=lambda v: json.dumps(v).encode('utf-8')
            )
            
            await self.consumer.start()
            await self.producer.start()
            
            logger.info("Kafka client initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Kafka client: {e}")
            raise

    async def close(self):
        """Close Kafka connections."""
        self._running = False
        logger.info("Closing Kafka client...")

        # Close consumer with timeout
        if self.consumer:
            try:
                await asyncio.wait_for(self.consumer.stop(), timeout=10.0)
                logger.info("Kafka consumer stopped")
            except asyncio.TimeoutError:
                logger.warning("Kafka consumer stop timed out")
            except Exception as e:
                logger.warning(f"Error stopping Kafka consumer: {e}")

        # Close producer with timeout
        if self.producer:
            try:
                await asyncio.wait_for(self.producer.stop(), timeout=5.0)
                logger.info("Kafka producer stopped")
            except asyncio.TimeoutError:
                logger.warning("Kafka producer stop timed out")
            except Exception as e:
                logger.warning(f"Error stopping Kafka producer: {e}")

        logger.info("Kafka client closed")

    def register_handler(self, topic_pattern: str, handler: Callable):
        """Register a message handler for a topic pattern."""
        self.message_handlers[topic_pattern] = handler
        logger.info(f"Registered handler for topic pattern: {topic_pattern}")

    async def subscribe_to_topics(self, topics: List[str]):
        """Subscribe to Kafka topics."""
        if not self.consumer:
            raise RuntimeError("Kafka consumer not initialized")

        self.consumer.subscribe(topics)
        logger.info(f"Subscribed to topics: {topics}")

    async def subscribe_to_pattern(self, pattern: str):
        """Subscribe to Kafka topics using a regex pattern."""
        if not self.consumer:
            raise RuntimeError("Kafka consumer not initialized")

        self.consumer.subscribe(pattern=pattern)
        logger.info(f"Subscribed to topic pattern: {pattern}")

    async def start_consuming(self):
        """Start consuming messages from subscribed topics."""
        if not self.consumer:
            raise RuntimeError("Kafka consumer not initialized")

        self._running = True
        logger.info("Starting Kafka message consumption")

        try:
            async for message in self.consumer:
                if not self._running:
                    logger.info("Stopping message consumption due to shutdown signal")
                    break

                await self._process_message(message)

        except asyncio.CancelledError:
            logger.info("Kafka message consumption cancelled")
            raise
        except Exception as e:
            logger.error(f"Error during message consumption: {e}")
            raise
        finally:
            logger.info("Kafka message consumption stopped")

    async def _process_message(self, message):
        """Process a received Kafka message."""
        try:
            topic = message.topic
            value = message.value
            
            logger.debug(f"Received message from topic {topic}: {value}")
            
            # Find matching handler
            handler = None
            for pattern, h in self.message_handlers.items():
                # Check if the topic matches the handler pattern
                # For "usecases" pattern, match topics containing "usecases"
                if pattern in topic:
                    handler = h
                    break
            
            if handler:
                await handler(topic, value)
            else:
                logger.warning(f"No handler found for topic: {topic}")
                
        except Exception as e:
            logger.error(f"Error processing message: {e}")

    async def produce_message(self, topic: str, message: Dict[str, Any]):
        """Produce a message to a Kafka topic."""
        if not self.producer:
            raise RuntimeError("Kafka producer not initialized")
            
        try:
            await self.producer.send_and_wait(topic, message)
            logger.debug(f"Sent message to topic {topic}: {message}")
            
        except Exception as e:
            logger.error(f"Error sending message to topic {topic}: {e}")
            raise

    async def send_kafka_message(self, topic: str, kafka_message: KafkaMessage):
        """Send a KafkaMessage to a topic."""
        message_dict = kafka_message.model_dump()
        await self.produce_message(topic, message_dict)

    def parse_building_data(self, data: Dict[str, Any]) -> BuildingData:
        """Parse raw data into BuildingData model."""
        return BuildingData(**data)

    def parse_address_data(self, data: Dict[str, Any]) -> AddressData:
        """Parse raw data into AddressData model."""
        return AddressData(**data)

    async def get_topics(self) -> List[str]:
        """Get list of available topics."""
        if not self.consumer:
            raise RuntimeError("Kafka consumer not initialized")

        try:
            # Use the consumer's internal client to fetch metadata
            cluster = self.consumer._client.cluster
            await self.consumer._client.force_metadata_update()
            return list(cluster.topics())
        except Exception as e:
            logger.error(f"Error fetching topics: {e}")
            return []
