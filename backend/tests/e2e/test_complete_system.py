"""
End-to-end tests for the complete RDF Agent System.
Tests the full workflow from document upload to query response.
"""
import pytest
import asyncio
from pathlib import Path
from typing import List, Dict, Any

from core.dependencies import get_dependencies, cleanup_dependencies
from agents.orchestrator_agent import OrchestratorAgent
from services.docling_service import DoclingService
from services.kafka_processor import KafkaProcessor
from models.data_models import QueryRequest, QueryResponse
from tests.utils.cleanup import DataCleanup
from tests.utils.helpers import TestDataGenerator, FileHelpers, AsyncTestHelpers


@pytest.mark.e2e
@pytest.mark.requires_services
@pytest.mark.requires_ai
class TestCompleteSystemE2E:
    """End-to-end tests for the complete RDF Agent System."""
    
    @pytest.fixture(scope="class")
    async def dependencies(self):
        """Get real dependencies for E2E testing."""
        deps = await get_dependencies()
        yield deps
        await cleanup_dependencies()
    
    @pytest.fixture(scope="class")
    async def cleanup_util(self, dependencies):
        """Create cleanup utility for test data."""
        return DataCleanup(
            dependencies.minio_client,
            dependencies.qdrant_client,
            dependencies.graphdb_client
        )
    
    @pytest.fixture(autouse=True)
    async def setup_and_cleanup(self, cleanup_util):
        """Setup and cleanup test data before and after each test."""
        # Cleanup before test
        await cleanup_util.cleanup_all()
        yield
        # Cleanup after test
        await cleanup_util.cleanup_all()
    
    @pytest.fixture
    async def orchestrator_agent(self, dependencies):
        """Create orchestrator agent for E2E testing."""
        return OrchestratorAgent(dependencies)
    
    @pytest.fixture
    def sample_documents(self, temp_dir: Path) -> List[Path]:
        """Create comprehensive sample documents for E2E testing."""
        documents = []
        
        # Energy efficiency document
        energy_doc = temp_dir / "energy_efficiency_guide.pdf"
        FileHelpers.create_test_pdf(
            energy_doc,
            """Energy Efficiency in Buildings
            
            This comprehensive guide covers various aspects of energy efficiency in modern buildings.
            
            LED Lighting Systems:
            LED lighting can reduce energy consumption by up to 75% compared to traditional incandescent bulbs.
            Smart LED systems with sensors can provide additional savings of 20-30%.
            
            Insulation and Building Envelope:
            Proper insulation is crucial for maintaining indoor temperature and reducing HVAC energy usage.
            High-performance windows can reduce heat loss by 40-50%.
            
            HVAC Systems:
            Modern HVAC systems with variable speed drives can improve efficiency by 30-40%.
            Regular maintenance and proper sizing are essential for optimal performance.
            
            Renewable Energy Integration:
            Solar panels can offset 50-100% of building energy consumption.
            Wind turbines are suitable for larger commercial buildings.
            
            Energy Rating Standards:
            Buildings with A+ energy rating consume 80% less energy than standard buildings.
            LEED certification provides comprehensive sustainability guidelines.
            """
        )
        documents.append(energy_doc)
        
        # Sustainability report
        sustainability_doc = temp_dir / "sustainability_report_2024.pdf"
        FileHelpers.create_test_pdf(
            sustainability_doc,
            """Sustainability Report 2024
            
            Executive Summary:
            This report presents findings on sustainable building practices and their environmental impact.
            
            Green Building Materials:
            Recycled steel reduces carbon footprint by 60%.
            Bamboo flooring is a sustainable alternative to hardwood.
            Low-VOC paints improve indoor air quality.
            
            Water Conservation:
            Rainwater harvesting systems can reduce water consumption by 30-50%.
            Low-flow fixtures decrease water usage without compromising functionality.
            
            Waste Management:
            Construction waste recycling can divert 75% of materials from landfills.
            Composting systems reduce organic waste by 80%.
            
            Carbon Footprint Reduction:
            Green roofs can reduce building energy consumption by 15-20%.
            Electric vehicle charging stations support sustainable transportation.
            
            Certification Programs:
            BREEAM certification ensures comprehensive environmental performance.
            Energy Star ratings help identify efficient buildings.
            """
        )
        documents.append(sustainability_doc)
        
        return documents
    
    @pytest.fixture
    async def sample_rdf_data(self, dependencies) -> str:
        """Create and load comprehensive RDF data."""
        ttl_content = """
        @prefix ex: <http://example.org/> .
        @prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
        @prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .
        @prefix xsd: <http://www.w3.org/2001/XMLSchema#> .
        
        # Building entities
        ex:GreenTowerOffice rdf:type ex:Building ;
            rdfs:label "Green Tower Office Complex" ;
            ex:energyRating "A+" ;
            ex:hasAddress ex:GreenTowerAddress ;
            ex:buildingType "Commercial" ;
            ex:yearBuilt "2020"^^xsd:int ;
            ex:totalArea "50000"^^xsd:int ;
            ex:hasCertification ex:LEEDPlatinum .
        
        ex:EcoResidential rdf:type ex:Building ;
            rdfs:label "Eco Residential Complex" ;
            ex:energyRating "A" ;
            ex:hasAddress ex:EcoResidentialAddress ;
            ex:buildingType "Residential" ;
            ex:yearBuilt "2019"^^xsd:int ;
            ex:totalArea "25000"^^xsd:int ;
            ex:hasCertification ex:BREEAMExcellent .
        
        ex:SmartOfficeBuilding rdf:type ex:Building ;
            rdfs:label "Smart Office Building" ;
            ex:energyRating "A++" ;
            ex:hasAddress ex:SmartOfficeAddress ;
            ex:buildingType "Commercial" ;
            ex:yearBuilt "2022"^^xsd:int ;
            ex:totalArea "75000"^^xsd:int ;
            ex:hasCertification ex:LEEDPlatinum ;
            ex:hasFeature ex:SolarPanels ;
            ex:hasFeature ex:SmartHVAC .
        
        # Address entities
        ex:GreenTowerAddress rdf:type ex:Address ;
            ex:street "123 Sustainability Ave" ;
            ex:city "Green City" ;
            ex:country "EcoLand" ;
            ex:postalCode "12345" .
        
        ex:EcoResidentialAddress rdf:type ex:Address ;
            ex:street "456 Renewable St" ;
            ex:city "Green City" ;
            ex:country "EcoLand" ;
            ex:postalCode "12346" .
        
        ex:SmartOfficeAddress rdf:type ex:Address ;
            ex:street "789 Innovation Blvd" ;
            ex:city "Tech City" ;
            ex:country "EcoLand" ;
            ex:postalCode "12347" .
        
        # Certification entities
        ex:LEEDPlatinum rdf:type ex:Certification ;
            rdfs:label "LEED Platinum" ;
            ex:certificationBody "USGBC" ;
            ex:validUntil "2030-12-31"^^xsd:date .
        
        ex:BREEAMExcellent rdf:type ex:Certification ;
            rdfs:label "BREEAM Excellent" ;
            ex:certificationBody "BRE" ;
            ex:validUntil "2029-12-31"^^xsd:date .
        
        # Feature entities
        ex:SolarPanels rdf:type ex:EnergyFeature ;
            rdfs:label "Solar Panel System" ;
            ex:capacity "500"^^xsd:int ;
            ex:unit "kW" .
        
        ex:SmartHVAC rdf:type ex:EnergyFeature ;
            rdfs:label "Smart HVAC System" ;
            ex:efficiency "95"^^xsd:int ;
            ex:unit "percent" .
        """
        
        # Load TTL data into GraphDB
        success = await dependencies.graphdb_client.upload_ttl_data(ttl_content)
        assert success, "Failed to load RDF data into GraphDB"
        
        return ttl_content
    
    @pytest.mark.asyncio
    async def test_complete_document_to_query_workflow(
        self,
        dependencies,
        orchestrator_agent: OrchestratorAgent,
        sample_documents: List[Path],
        sample_rdf_data: str
    ):
        """Test the complete workflow from document upload to query response."""
        docling_service = DoclingService(dependencies)
        
        # Step 1: Upload and process documents
        for doc_path in sample_documents:
            filename = doc_path.name
            with open(doc_path, 'rb') as f:
                content = f.read()
            
            # Upload to MinIO
            upload_success = await dependencies.minio_client.put_object(filename, content)
            assert upload_success, f"Failed to upload {filename}"
            
            # Process with Docling
            processing_result = await docling_service.process_document(filename)
            assert processing_result is not None
            assert processing_result.get("success", False)
        
        # Step 2: Wait for document processing to complete
        await AsyncTestHelpers.wait_for_condition(
            lambda: self._check_embeddings_created(dependencies),
            timeout=60.0
        )
        
        # Step 3: Test various query types
        test_queries = [
            {
                "query": "What are the benefits of LED lighting for energy efficiency?",
                "query_type": "natural_language",
                "expected_keywords": ["led", "energy", "efficiency", "lighting"]
            },
            {
                "query": "How much can solar panels reduce energy consumption?",
                "query_type": "natural_language", 
                "expected_keywords": ["solar", "energy", "consumption", "reduce"]
            },
            {
                "query": "Find all buildings with A+ energy rating",
                "query_type": "natural_language",
                "expected_keywords": ["building", "energy", "rating", "a+"]
            },
            {
                "query": "What certifications do the buildings have?",
                "query_type": "natural_language",
                "expected_keywords": ["certification", "leed", "breeam"]
            }
        ]
        
        for test_query in test_queries:
            query_request = QueryRequest(
                query=test_query["query"],
                query_type=test_query["query_type"],
                session_id="e2e-test-session"
            )
            
            response = await orchestrator_agent.process_query(query_request)
            
            # Validate response
            assert isinstance(response, QueryResponse)
            assert response.success is True
            assert response.response is not None
            assert len(response.response) > 0
            
            # Check for expected keywords
            response_lower = response.response.lower()
            found_keywords = [
                keyword for keyword in test_query["expected_keywords"]
                if keyword in response_lower
            ]
            assert len(found_keywords) > 0, f"No expected keywords found in response for query: {test_query['query']}"
    
    async def _check_embeddings_created(self, dependencies) -> bool:
        """Check if embeddings have been created in Qdrant."""
        try:
            point_count = await dependencies.qdrant_client.count_points()
            return point_count > 0
        except Exception:
            return False
    
    @pytest.mark.asyncio
    async def test_multi_session_conversation_flow(
        self,
        orchestrator_agent: OrchestratorAgent,
        sample_documents: List[Path],
        sample_rdf_data: str
    ):
        """Test multi-session conversation flow with context preservation."""
        # Session 1: Initial queries
        session1_queries = [
            "What is energy efficiency?",
            "Tell me about LED lighting benefits",
            "How much energy can LEDs save?"
        ]
        
        session1_history = []
        for query in session1_queries:
            query_request = QueryRequest(
                query=query,
                query_type="natural_language",
                session_id="session-1",
                conversation_history=session1_history.copy()
            )
            
            response = await orchestrator_agent.process_query(query_request)
            assert response.success is True
            
            # Update conversation history
            session1_history.extend([
                {"role": "user", "content": query},
                {"role": "assistant", "content": response.response}
            ])
        
        # Session 2: Different topic
        session2_queries = [
            "Find buildings with LEED certification",
            "What are the addresses of these buildings?",
            "When were they built?"
        ]
        
        session2_history = []
        for query in session2_queries:
            query_request = QueryRequest(
                query=query,
                query_type="natural_language",
                session_id="session-2",
                conversation_history=session2_history.copy()
            )
            
            response = await orchestrator_agent.process_query(query_request)
            assert response.success is True
            
            session2_history.extend([
                {"role": "user", "content": query},
                {"role": "assistant", "content": response.response}
            ])
        
        # Verify sessions are independent
        assert len(session1_history) == 6  # 3 queries * 2 messages each
        assert len(session2_history) == 6  # 3 queries * 2 messages each
    
    @pytest.mark.asyncio
    async def test_error_recovery_and_resilience(
        self,
        dependencies,
        orchestrator_agent: OrchestratorAgent
    ):
        """Test system error recovery and resilience."""
        # Test with invalid/problematic queries
        problematic_queries = [
            "This is a very ambiguous query with no clear intent",
            "Find me something that doesn't exist in the data",
            "Execute this invalid SPARQL: INVALID SYNTAX HERE",
            ""  # Empty query
        ]
        
        for query in problematic_queries:
            query_request = QueryRequest(
                query_id=f"error-test-{hash(query)}",
                query_text=query,
                query_type="natural_language",
                session_id="error-test-session"
            )
            
            response = await orchestrator_agent.process_query(query_request)
            
            # System should handle errors gracefully
            assert isinstance(response, QueryResponse)
            assert response.response is not None
            # Either success with helpful message or controlled failure
            if not response.success:
                assert response.error_message is not None
    
    @pytest.mark.asyncio
    async def test_performance_under_load(
        self,
        orchestrator_agent: OrchestratorAgent,
        sample_rdf_data: str
    ):
        """Test system performance under concurrent load."""
        # Create multiple concurrent queries
        concurrent_queries = [
            "Find all buildings with A+ rating",
            "What are the energy efficiency benefits of LED lighting?",
            "List buildings in Green City",
            "What certifications do buildings have?",
            "How can solar panels reduce energy consumption?",
            "Find buildings built after 2020",
            "What are sustainable building materials?",
            "How much energy can proper insulation save?"
        ]
        
        # Execute queries concurrently
        import time
        start_time = time.time()
        
        tasks = []
        for i, query in enumerate(concurrent_queries):
            query_request = QueryRequest(
                query=query,
                query_type="natural_language",
                session_id=f"load-test-session-{i}"
            )
            tasks.append(orchestrator_agent.process_query(query_request))
        
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        total_time = end_time - start_time
        
        # Validate all responses
        successful_responses = 0
        for i, response in enumerate(responses):
            if isinstance(response, Exception):
                print(f"Query {i} failed with exception: {response}")
            else:
                assert isinstance(response, QueryResponse)
                if response.success:
                    successful_responses += 1
        
        # At least 75% of queries should succeed
        success_rate = successful_responses / len(concurrent_queries)
        assert success_rate >= 0.75, f"Success rate too low: {success_rate}"
        
        # Performance should be reasonable (adjust threshold as needed)
        avg_time_per_query = total_time / len(concurrent_queries)
        assert avg_time_per_query < 30.0, f"Average query time too high: {avg_time_per_query}s"
    
    @pytest.mark.asyncio
    async def test_data_consistency_across_components(
        self,
        dependencies,
        orchestrator_agent: OrchestratorAgent,
        sample_documents: List[Path],
        sample_rdf_data: str
    ):
        """Test data consistency across all system components."""
        # Process documents
        docling_service = DoclingService(dependencies)
        for doc_path in sample_documents:
            filename = doc_path.name
            with open(doc_path, 'rb') as f:
                content = f.read()
            
            await dependencies.minio_client.put_object(filename, content)
            await docling_service.process_document(filename)
        
        # Wait for processing
        await AsyncTestHelpers.wait_for_condition(
            lambda: self._check_embeddings_created(dependencies),
            timeout=60.0
        )
        
        # Test consistency between RAG and RDF data
        rag_query = QueryRequest(
            query="What information do we have about energy efficiency?",
            query_type="natural_language",
            session_id="consistency-test"
        )
        
        rdf_query = QueryRequest(
            query="Find all buildings and their energy ratings",
            query_type="natural_language", 
            session_id="consistency-test"
        )
        
        rag_response = await orchestrator_agent.process_query(rag_query)
        rdf_response = await orchestrator_agent.process_query(rdf_query)
        
        assert rag_response.success is True
        assert rdf_response.success is True
        
        # Both responses should contain relevant information
        assert "energy" in rag_response.response.lower()
        assert "building" in rdf_response.response.lower()
    
    @pytest.mark.asyncio
    async def test_system_health_and_monitoring(
        self,
        dependencies,
        orchestrator_agent: OrchestratorAgent
    ):
        """Test system health checks and monitoring capabilities."""
        # Test individual component health
        minio_health = await dependencies.minio_client.health_check()
        qdrant_health = await dependencies.qdrant_client.health_check()
        graphdb_health = await dependencies.graphdb_client.health_check()
        
        assert minio_health is True, "MinIO health check failed"
        assert qdrant_health is True, "Qdrant health check failed"
        assert graphdb_health is True, "GraphDB health check failed"
        
        # Test agent health
        orchestrator_health = await orchestrator_agent.health_check()
        assert orchestrator_health is True, "Orchestrator agent health check failed"
        
        # Test system-wide health through a simple query
        health_query = QueryRequest(
            query="System health check query",
            query_type="natural_language",
            session_id="health-check"
        )
        
        response = await orchestrator_agent.process_query(health_query)
        assert isinstance(response, QueryResponse)
        # Response should be generated (success or controlled failure)
        assert response.response is not None
