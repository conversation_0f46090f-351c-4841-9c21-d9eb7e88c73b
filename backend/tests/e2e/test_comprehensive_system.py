#!/usr/bin/env python3
"""
Comprehensive end-to-end system testing for the RDF Agent System.
This script runs a complete end-to-end test of all system components.
"""
import asyncio
import json
import logging
import sys
import time
from pathlib import Path
from typing import Dict, List, Any
import pytest

# Add the backend directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from core.dependencies import get_dependencies, cleanup_dependencies
from services.kafka_processor import KafkaProcessor
from agents.orchestrator_agent import OrchestratorAgent
from models.data_models import QueryRequest, QueryResponse
from tests.utils.cleanup import DataCleanup

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ComprehensiveSystemTester:
    """Comprehensive system tester for the RDF Agent System."""
    
    def __init__(self):
        self.dependencies = None
        self.cleanup_util = None
        self.test_results = {
            "kafka_processing": False,
            "document_processing": False,
            "rdf_storage": False,
            "vector_storage": False,
            "ai_query": False,
            "end_to_end": False
        }
    
    async def setup(self):
        """Set up test environment."""
        logger.info("Setting up test environment...")
        
        try:
            # Get dependencies
            self.dependencies = await get_dependencies()
            
            # Create cleanup utility
            self.cleanup_util = DataCleanup(
                self.dependencies.minio_client,
                self.dependencies.qdrant_client,
                self.dependencies.graphdb_client
            )
            
            # Clean up any existing test data
            await self.cleanup_util.cleanup_all()
            
            logger.info("Test environment setup completed")
            return True
            
        except Exception as e:
            logger.error(f"Failed to set up test environment: {e}")
            return False
    
    async def cleanup(self):
        """Clean up test environment."""
        logger.info("Cleaning up test environment...")
        
        try:
            if self.cleanup_util:
                await self.cleanup_util.cleanup_all()
            
            if self.dependencies:
                await cleanup_dependencies()
            
            logger.info("Test environment cleanup completed")
            
        except Exception as e:
            logger.error(f"Failed to clean up test environment: {e}")
    
    def load_sample_data(self) -> List[Dict[str, Any]]:
        """Load sample data from the example file."""
        example_file = Path(__file__).parent.parent / ".context" / "example_files" / "example_input.json"
        
        if not example_file.exists():
            logger.warning("Example file not found, using fallback data")
            return [
                {
                    "graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .",
                    "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\n@prefix inst: <https://example.com/>.\n\ninst:test-address-1 ibpdi:hasBuilding inst:test-building-1.\n",
                    "accessRights": None,
                    "useCase": "TestPortfolio",
                    "graphMetadata": [
                        {
                            "id": "test-address-1",
                            "classType": "https://ibpdi.datacat.org/class/Address",
                            "propertiesValues": {
                                "StreetName": "Test Street",
                                "HouseNumber": "123",
                                "PostalCode": "12345",
                                "City": "Test City",
                                "Country": "Test Country"
                            }
                        },
                        {
                            "id": "test-building-1",
                            "classType": "https://ibpdi.datacat.org/class/Building",
                            "propertiesValues": {
                                "PrimaryTypeOfBuilding": "Office",
                                "BuildingCode": "TEST001",
                                "Name": "Test Building",
                                "ValidFrom": "44926",
                                "PrimaryHeatingType": "District heating",
                                "ParkingSpaces": "50",
                                "EnergyEfficiencyClass": "A+",
                                "ConstructionYear": "2020"
                            }
                        }
                    ]
                }
            ]
        
        with open(example_file, 'r') as f:
            data = json.load(f)
        
        # Return first 3 items for testing
        return data[:3]
    
    async def test_kafka_processing(self) -> bool:
        """Test Kafka message processing."""
        logger.info("Testing Kafka message processing...")
        
        try:
            sample_data = self.load_sample_data()
            processor = KafkaProcessor(self.dependencies)
            
            processed_count = 0
            for i, item in enumerate(sample_data):
                logger.info(f"Processing item {i+1}/{len(sample_data)}")
                
                # Extract and process building/address data
                for metadata_item in item.get("graphMetadata", []):
                    class_type = metadata_item.get("classType", "")
                    
                    if "Building" in class_type:
                        building_data = {
                            "building_id": metadata_item["id"],
                            "usecase_name": item.get("useCase", "TestCase"),
                            "properties": metadata_item.get("propertiesValues", {}),
                            "graph_data": item.get("graphData", ""),
                            "metadata": metadata_item
                        }
                        await processor._handle_building_message("building_data", building_data)
                        processed_count += 1
                        
                    elif "Address" in class_type:
                        address_data = {
                            "address_id": metadata_item["id"],
                            "usecase_name": item.get("useCase", "TestCase"),
                            "properties": metadata_item.get("propertiesValues", {}),
                            "graph_data": item.get("graphData", ""),
                            "metadata": metadata_item
                        }
                        await processor._handle_address_message("address_data", address_data)
                        processed_count += 1
                
                # Small delay between items
                await asyncio.sleep(1)
            
            logger.info(f"Processed {processed_count} data items")
            self.test_results["kafka_processing"] = processed_count > 0
            return processed_count > 0
            
        except Exception as e:
            logger.error(f"Kafka processing test failed: {e}")
            return False
    
    async def test_document_processing(self) -> bool:
        """Test document processing workflow."""
        logger.info("Testing document processing...")
        
        try:
            # Create test documents
            test_content = """
            Energy Efficiency in Buildings
            
            LED lighting systems can reduce energy consumption by up to 75%.
            Smart HVAC systems improve efficiency by 30-40%.
            Solar panels can offset 50-100% of building energy consumption.
            Buildings with A+ energy rating consume 80% less energy.
            """
            
            processor = KafkaProcessor(self.dependencies)
            
            # Upload test document to MinIO
            object_path = "documents/TestCase/test/energy_efficiency.txt"
            await self.dependencies.minio_client.upload_data(test_content.encode(), object_path)
            
            # Process document
            document_message = {
                "usecase_name": "TestCase",
                "folders": ["test"],
                "files": [{
                    "filename": "energy_efficiency.txt",
                    "object_path": object_path
                }]
            }
            
            await processor._handle_document_message("document_processing", document_message)
            
            # Wait for processing
            await asyncio.sleep(5)
            
            logger.info("Document processing completed")
            self.test_results["document_processing"] = True
            return True
            
        except Exception as e:
            logger.error(f"Document processing test failed: {e}")
            return False
    
    async def test_storage_systems(self) -> bool:
        """Test RDF and vector storage systems."""
        logger.info("Testing storage systems...")
        
        try:
            # Test GraphDB storage
            sparql_query = """
            SELECT ?s ?p ?o WHERE {
                ?s ?p ?o .
            } LIMIT 10
            """
            
            result = await self.dependencies.graphdb_client.execute_sparql_query(sparql_query)
            if result and result.get("results", {}).get("bindings"):
                logger.info(f"Found {len(result['results']['bindings'])} triples in GraphDB")
                self.test_results["rdf_storage"] = True
            else:
                logger.warning("No RDF data found in GraphDB")
            
            # Test Qdrant storage
            try:
                point_count = await self.dependencies.qdrant_client.count_points()
                logger.info(f"Found {point_count} points in Qdrant")
                self.test_results["vector_storage"] = point_count > 0
            except Exception as e:
                logger.warning(f"Qdrant check failed: {e}")
            
            return self.test_results["rdf_storage"] or self.test_results["vector_storage"]
            
        except Exception as e:
            logger.error(f"Storage systems test failed: {e}")
            return False
    
    async def test_ai_query_system(self) -> bool:
        """Test AI query system."""
        logger.info("Testing AI query system...")
        
        try:
            # Create a test session first
            session_id = "test-session"
            try:
                await self.dependencies.session_manager.create_session(session_id)
            except Exception as e:
                logger.warning(f"Could not create session: {e}")

            orchestrator = OrchestratorAgent(self.dependencies)

            # Test queries
            test_queries = [
                "What buildings do we have?",
                "Find buildings with good energy efficiency",
                "What information do we have about energy efficiency?"
            ]
            
            successful_queries = 0
            for query in test_queries:
                try:
                    import uuid
                    query_request = QueryRequest(
                        query_id=str(uuid.uuid4()),
                        query_text=query,
                        query_type="natural_language",
                        session_id="test-session"
                    )
                    
                    response = await orchestrator.process_query(query_request)
                    
                    if isinstance(response, QueryResponse) and response.response_text:
                        logger.info(f"Query '{query}' succeeded")
                        successful_queries += 1
                    else:
                        logger.warning(f"Query '{query}' returned empty response")
                        
                except Exception as e:
                    logger.error(f"Query '{query}' failed: {e}")
            
            self.test_results["ai_query"] = successful_queries > 0
            logger.info(f"Successfully executed {successful_queries}/{len(test_queries)} queries")
            return successful_queries > 0
            
        except Exception as e:
            logger.error(f"AI query system test failed: {e}")
            return False
    
    @pytest.mark.asyncio
    async def run_comprehensive_test(self) -> bool:
        """Run comprehensive system test."""
        logger.info("Starting comprehensive system test...")
        
        try:
            # Setup
            if not await self.setup():
                return False
            
            # Test individual components
            logger.info("=" * 60)
            logger.info("PHASE 1: Testing Kafka Processing")
            kafka_success = await self.test_kafka_processing()
            
            logger.info("=" * 60)
            logger.info("PHASE 2: Testing Document Processing")
            doc_success = await self.test_document_processing()
            
            # Wait for all processing to complete
            logger.info("Waiting for processing to complete...")
            await asyncio.sleep(10)
            
            logger.info("=" * 60)
            logger.info("PHASE 3: Testing Storage Systems")
            storage_success = await self.test_storage_systems()
            
            logger.info("=" * 60)
            logger.info("PHASE 4: Testing AI Query System")
            ai_success = await self.test_ai_query_system()
            
            # Overall success
            self.test_results["end_to_end"] = all([
                kafka_success,
                doc_success or storage_success,  # At least one storage system should work
                ai_success
            ])
            
            return self.test_results["end_to_end"]
            
        except Exception as e:
            logger.error(f"Comprehensive test failed: {e}")
            return False
        
        finally:
            await self.cleanup()
    
    def print_results(self):
        """Print test results summary."""
        logger.info("=" * 60)
        logger.info("TEST RESULTS SUMMARY")
        logger.info("=" * 60)
        
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            logger.info(f"{test_name.replace('_', ' ').title()}: {status}")
        
        overall_status = "✅ PASS" if self.test_results["end_to_end"] else "❌ FAIL"
        logger.info(f"\nOverall System Test: {overall_status}")
        logger.info("=" * 60)


@pytest.mark.asyncio
async def test_comprehensive_system():
    """Main test execution function."""
    logger.info("Starting comprehensive system testing...")

    tester = ComprehensiveSystemTester()

    try:
        success = await tester.run_comprehensive_test()
        tester.print_results()

        assert success, "Comprehensive system test failed"
        logger.info("🎉 All tests passed! System is working correctly.")

    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        await tester.cleanup()
        raise


async def main():
    """Main test execution function for standalone execution."""
    logger.info("Starting comprehensive system testing...")

    tester = ComprehensiveSystemTester()

    try:
        success = await tester.run_comprehensive_test()
        tester.print_results()

        if success:
            logger.info("🎉 All tests passed! System is working correctly.")
            sys.exit(0)
        else:
            logger.error("❌ Some tests failed. Please check the logs above.")
            sys.exit(1)

    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
        await tester.cleanup()
        sys.exit(1)
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        await tester.cleanup()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
