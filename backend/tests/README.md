# RDF Agent System - Testing Documentation

This directory contains comprehensive tests for the RDF Agent System, organized into unit tests, integration tests, and end-to-end tests.

## Test Structure

```
tests/
├── conftest.py              # Global pytest configuration and fixtures
├── utils/                   # Test utilities and helpers
│   ├── __init__.py
│   ├── cleanup.py          # Cleanup utilities for test data
│   └── helpers.py          # Test helper functions and data generators
├── unit/                   # Unit tests (fast, isolated)
│   ├── __init__.py
│   ├── test_orchestrator_agent.py
│   ├── test_rdf_query_agent.py
│   └── test_rag_agent.py
├── integration/            # Integration tests (require services)
│   ├── __init__.py
│   ├── test_document_processing.py
│   └── test_rdf_operations.py
└── e2e/                   # End-to-end tests (full system)
    ├── __init__.py
    └── test_complete_system.py
```

## Test Categories

### Unit Tests (`tests/unit/`)
- **Purpose**: Test individual components in isolation
- **Speed**: Fast (< 1 second per test)
- **Dependencies**: Mocked external services
- **Coverage**: Agent logic, data models, utilities

**Key Features:**
- Mock all external dependencies (MinIO, Qdrant, GraphDB, AI models)
- Test agent initialization and configuration
- Test error handling and edge cases
- Test tool registration and functionality

### Integration Tests (`tests/integration/`)
- **Purpose**: Test component interactions with real services
- **Speed**: Medium (1-30 seconds per test)
- **Dependencies**: Real external services required
- **Coverage**: Service integrations, data flow, API interactions

**Key Features:**
- Test document processing pipeline (MinIO → Docling → Qdrant)
- Test RDF operations (TTL loading → GraphDB → SPARQL queries)
- Test concurrent operations and performance
- Test error recovery and resilience

### End-to-End Tests (`tests/e2e/`)
- **Purpose**: Test complete system workflows
- **Speed**: Slow (30+ seconds per test)
- **Dependencies**: All services + AI model access
- **Coverage**: Full user journeys, system integration

**Key Features:**
- Test complete document-to-query workflows
- Test multi-session conversations
- Test system performance under load
- Test data consistency across components

## Test Markers

Tests are organized using pytest markers:

- `@pytest.mark.unit` - Unit tests
- `@pytest.mark.integration` - Integration tests  
- `@pytest.mark.e2e` - End-to-end tests
- `@pytest.mark.requires_services` - Tests requiring external services
- `@pytest.mark.requires_ai` - Tests requiring AI model access
- `@pytest.mark.slow` - Slow-running tests

## Running Tests

### Quick Start

```bash
# Install dependencies
make install

# Run unit tests (fastest)
make test-unit

# Run all tests
make test-all

# Generate coverage report
make test-coverage
```

### Detailed Commands

```bash
# Using the test runner script
python scripts/run_tests.py unit                    # Unit tests only
python scripts/run_tests.py integration             # Integration tests only
python scripts/run_tests.py e2e                     # E2E tests only
python scripts/run_tests.py all                     # All tests
python scripts/run_tests.py coverage                # Coverage report

# Using pytest directly
uv run pytest tests/unit/ -m unit                   # Unit tests
uv run pytest tests/integration/ -m integration     # Integration tests
uv run pytest tests/e2e/ -m e2e                     # E2E tests
uv run pytest --cov=. --cov-report=html            # With coverage

# Run specific test files
uv run pytest tests/unit/test_orchestrator_agent.py -v
uv run pytest tests/integration/test_document_processing.py::TestDocumentProcessingIntegration::test_complete_document_processing_pipeline -v
```

### Test Markers Usage

```bash
# Run only tests that require services
uv run pytest -m requires_services

# Run tests that don't require AI
uv run pytest -m "not requires_ai"

# Run fast tests only
uv run pytest -m "not slow"

# Combine markers
uv run pytest -m "integration and not slow"
```

## Test Data Management

### Cleanup Utilities

The test suite includes comprehensive cleanup utilities to ensure clean test environments:

```bash
# Clean all test data
python scripts/cleanup_test_data.py all

# Clean specific services
python scripts/cleanup_test_data.py minio
python scripts/cleanup_test_data.py qdrant
python scripts/cleanup_test_data.py graphdb

# Clean by prefix
python scripts/cleanup_test_data.py prefix test_
```

### Automatic Cleanup

- Tests automatically clean up before and after execution
- Each test class has `setup_and_cleanup` fixtures
- MinIO: Removes JSON/Markdown files (keeps PDFs)
- Qdrant: Clears test collections and embeddings
- GraphDB: Removes test RDF data

## Test Configuration

### Environment Variables

Required for integration and E2E tests:

```bash
# AI Configuration
AI__OR_API_KEY=your_openrouter_api_key

# MinIO Configuration
MINIO__ENDPOINT=localhost:9000
MINIO__ACCESS_KEY=minioadmin
MINIO__SECRET_KEY=minioadmin
MINIO__BUCKET_NAME=test-documents

# Qdrant Configuration
QDRANT__HOST=localhost
QDRANT__PORT=6333

# GraphDB Configuration
GRAPHDB__ENDPOINT=http://localhost:7200
GRAPHDB__REPOSITORY=test-repo

# Kafka Configuration
KAFKA__BOOTSTRAP_SERVERS=localhost:9092
```

### Service Dependencies

For integration and E2E tests, ensure these services are running:

1. **MinIO** (Object Storage)
   ```bash
   docker run -p 9000:9000 -p 9001:9001 minio/minio server /data --console-address ":9001"
   ```

2. **Qdrant** (Vector Database)
   ```bash
   docker run -p 6333:6333 qdrant/qdrant
   ```

3. **GraphDB** (RDF Database)
   ```bash
   docker run -p 7200:7200 ontotext/graphdb:10.0.0
   ```

4. **Kafka** (Message Queue)
   ```bash
   docker run -p 9092:9092 confluentinc/cp-kafka:latest
   ```

## Test Development Guidelines

### Writing Unit Tests

```python
@pytest.mark.unit
class TestMyComponent:
    @pytest.fixture
    def mock_dependencies(self):
        # Use the global mock_dependencies fixture
        pass
    
    @pytest.mark.asyncio
    async def test_my_function(self, mock_dependencies):
        # Test implementation
        pass
```

### Writing Integration Tests

```python
@pytest.mark.integration
@pytest.mark.requires_services
class TestMyIntegration:
    @pytest.fixture(scope="class")
    async def dependencies(self):
        deps = await get_dependencies()
        yield deps
        await cleanup_dependencies()
    
    @pytest.fixture(autouse=True)
    async def setup_and_cleanup(self, cleanup_util):
        await cleanup_util.cleanup_all()
        yield
        await cleanup_util.cleanup_all()
```

### Writing E2E Tests

```python
@pytest.mark.e2e
@pytest.mark.requires_services
@pytest.mark.requires_ai
class TestMyE2E:
    # Similar structure to integration tests
    # but with full system testing
```

## Coverage Requirements

- **Minimum Coverage**: 80%
- **Unit Tests**: Should achieve >90% coverage for core logic
- **Integration Tests**: Focus on service interactions
- **E2E Tests**: Focus on user workflows

## Performance Benchmarks

- **Unit Tests**: < 1 second per test
- **Integration Tests**: < 30 seconds per test
- **E2E Tests**: < 5 minutes per test
- **Full Test Suite**: < 15 minutes

## Troubleshooting

### Common Issues

1. **Service Connection Errors**
   - Ensure all required services are running
   - Check environment variables
   - Verify network connectivity

2. **Test Data Conflicts**
   - Run cleanup before tests: `make clean-test-data`
   - Check for leftover test data in services

3. **Timeout Issues**
   - Increase timeout for slow tests
   - Check service performance
   - Consider running tests in parallel

4. **AI Model Errors**
   - Verify OpenRouter API key
   - Check model availability
   - Consider using test models for unit tests

### Debug Mode

```bash
# Run tests with debugging
uv run pytest --pdb tests/unit/test_orchestrator_agent.py

# Run with verbose output
uv run pytest -v -s tests/

# Run only failed tests
uv run pytest --lf
```

## Continuous Integration

The test suite is designed for CI/CD environments:

```bash
# CI-friendly test run
make test-ci

# Generate reports for CI
uv run pytest --junitxml=test-results.xml --cov-report=xml
```

## Contributing

When adding new tests:

1. Follow the existing test structure
2. Use appropriate test markers
3. Include cleanup in integration/E2E tests
4. Add docstrings explaining test purpose
5. Ensure tests are deterministic and isolated
6. Update this README if adding new test categories

## Resources

- [pytest Documentation](https://docs.pytest.org/)
- [pytest-asyncio](https://pytest-asyncio.readthedocs.io/)
- [PydanticAI Testing](https://ai.pydantic.dev/testing/)
- [Coverage.py](https://coverage.readthedocs.io/)
