#!/usr/bin/env python3
"""
Unit tests for configuration and settings loading.
"""
import os
import logging
from pathlib import Path
import pytest

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_settings_loading():
    """Test that settings load correctly from .env and config.yaml."""
    try:
        from config.settings import Settings
        
        logger.info("Testing settings loading...")
        
        # Load settings
        settings = Settings()
        
        # Test that environment variables are loaded
        logger.info(f"✓ GraphDB URL: {settings.graphdb.url}")
        logger.info(f"✓ GraphDB Repository: {settings.graphdb.default_repository}")
        logger.info(f"✓ MinIO Endpoint: {settings.minio.endpoint}")
        logger.info(f"✓ MinIO Bucket: {settings.minio.bucket_name}")
        logger.info(f"✓ Qdrant Host: {settings.qdrant.host}:{settings.qdrant.port}")
        logger.info(f"✓ Kafka Servers: {settings.kafka.bootstrap_servers}")
        logger.info(f"✓ AI Model: {settings.ai.model}")
        logger.info(f"✓ Database URL: {settings.database.url}")
        logger.info(f"✓ Docling Enabled: {settings.docling.enabled}")
        
        # Test that API key is loaded (but don't log it)
        if settings.ai.api_key:
            logger.info("✓ AI API Key is set")
        else:
            logger.warning("⚠ AI API Key is not set")
        
        # Test nested settings
        assert settings.graphdb.url == "http://localhost:7200"
        assert settings.graphdb.default_repository == "test"
        assert settings.minio.endpoint == "localhost:9000"
        assert settings.minio.bucket_name == "data"
        assert settings.qdrant.host == "localhost"
        assert settings.qdrant.port == 6333
        
        logger.info("✓ All settings loaded correctly")

    except Exception as e:
        logger.error(f"Settings loading failed: {e}")
        import traceback
        traceback.print_exc()
        raise AssertionError(f"Settings loading failed: {e}")


def test_config_yaml():
    """Test that config.yaml exists and can be loaded."""
    try:
        import yaml
        
        config_path = Path("config/config.yaml")
        if not config_path.exists():
            logger.warning("config.yaml not found, creating default")
            return False
        
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        logger.info("✓ config.yaml loaded successfully")
        logger.info(f"  App name: {config.get('app', {}).get('name', 'Not set')}")
        logger.info(f"  App version: {config.get('app', {}).get('version', 'Not set')}")

    except Exception as e:
        logger.error(f"Config YAML loading failed: {e}")
        raise AssertionError(f"Config YAML loading failed: {e}")


def test_environment_variables():
    """Test that environment variables are properly set."""
    try:
        logger.info("Testing environment variables...")
        
        # Check critical environment variables
        env_vars = [
            "OPENAI_API_KEY",
            "AI__API_KEY", 
            "GRAPHDB__URL",
            "MINIO__ENDPOINT",
            "QDRANT__HOST"
        ]
        
        for var in env_vars:
            value = os.getenv(var)
            if value:
                if "API_KEY" in var:
                    logger.info(f"✓ {var}: [REDACTED]")
                else:
                    logger.info(f"✓ {var}: {value}")
            else:
                logger.warning(f"⚠ {var}: Not set")

    except Exception as e:
        logger.error(f"Environment variables test failed: {e}")
        raise AssertionError(f"Environment variables test failed: {e}")


def test_all_configuration():
    """Run all configuration tests."""
    logger.info("Starting configuration tests...")

    # Just call the individual test functions - they will raise AssertionError if they fail
    test_environment_variables()
    test_config_yaml()
    test_settings_loading()

    logger.info("✓ All configuration tests passed!")


def main():
    """Run all configuration tests for standalone execution."""
    logger.info("Starting configuration tests...")

    results = []
    results.append(test_environment_variables())
    results.append(test_config_yaml())
    results.append(test_settings_loading())

    if all(results):
        logger.info("✓ All configuration tests passed!")
        return True
    else:
        logger.error("✗ Some configuration tests failed!")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
