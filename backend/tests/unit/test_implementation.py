"""
Unit tests to verify the implementation works correctly.
"""
import asyncio
import logging
import json
from pathlib import Path
import pytest

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@pytest.mark.asyncio
async def test_basic_functionality():
    """Test basic functionality of the system."""
    deps = None
    try:
        from core.dependencies import get_dependencies, cleanup_dependencies

        logger.info("Testing basic system functionality...")

        # Initialize dependencies
        deps = await get_dependencies()
        logger.info("✓ Dependencies initialized")
        
        # Test configuration
        logger.info(f"✓ GraphDB URL: {deps.settings.graphdb.url}")
        logger.info(f"✓ Qdrant host: {deps.settings.qdrant.host}:{deps.settings.qdrant.port}")
        logger.info(f"✓ MinIO endpoint: {deps.settings.minio.endpoint}")
        logger.info(f"✓ Kafka servers: {deps.settings.kafka.bootstrap_servers}")
        
        # Test TTL conversion
        from services.ttl_converter import TTLConverter
        from models.data_models import BuildingData
        
        ttl_converter = TTLConverter()
        
        # Create sample building data
        building_data = BuildingData(
            building_id="test_building_001",
            name="Test Building",
            address="123 Test Street, Test City",
            coordinates={"lat": 52.5, "lon": 13.4},
            properties={"type": "office", "floors": 5}
        )
        
        # Convert to TTL
        ttl_result = await ttl_converter.convert_building_data(building_data)
        
        if ttl_result.success:
            logger.info("✓ TTL conversion successful")
            logger.info(f"  Generated {ttl_result.triples_count} triples")
        else:
            logger.error(f"✗ TTL conversion failed: {ttl_result.error_message}")
        
        # Test agents (basic initialization)
        from agents.orchestrator_agent import OrchestratorAgent
        from agents.rdf_query_agent import RDFQueryAgent
        from agents.rag_agent import RAGAgent
        
        orchestrator = OrchestratorAgent(deps)
        rdf_agent = RDFQueryAgent(deps)
        rag_agent = RAGAgent(deps)
        
        logger.info("✓ Agents initialized successfully")
        
        # Test health checks
        orchestrator_healthy = await orchestrator.health_check()
        logger.info(f"✓ Orchestrator health: {'healthy' if orchestrator_healthy else 'unhealthy'}")
        
        logger.info("Basic functionality test completed successfully!")
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if deps:
            from core.dependencies import cleanup_dependencies
            await cleanup_dependencies()


@pytest.mark.asyncio
async def test_sample_data_processing():
    """Test processing of sample data."""
    deps = None
    try:
        from core.dependencies import get_dependencies, cleanup_dependencies
        
        logger.info("Testing sample data processing...")
        
        # Check if sample data exists
        sample_file = Path("../.context/example_files/example_input.json")
        if not sample_file.exists():
            logger.warning("Sample data file not found, skipping test")
            return
        
        # Load sample data
        with open(sample_file, 'r') as f:
            sample_data = json.load(f)
        
        logger.info(f"Loaded {len(sample_data)} sample items")
        
        # Initialize dependencies
        deps = await get_dependencies()
        
        # Test TTL conversion for first few items
        from services.ttl_converter import TTLConverter
        from models.data_models import BuildingData, AddressData
        
        ttl_converter = TTLConverter()
        
        for i, item in enumerate(sample_data[:3]):  # Test first 3 items
            logger.info(f"Testing item {i+1}: {item.get('building_id', item.get('address_id', 'unknown'))}")
            
            try:
                if "building_id" in item:
                    building_data = BuildingData(**item)
                    result = await ttl_converter.convert_building_data(building_data)
                    if result.success:
                        logger.info(f"  ✓ Building TTL conversion: {result.triples_count} triples")
                    else:
                        logger.error(f"  ✗ Building TTL conversion failed: {result.error_message}")
                
                elif "address_id" in item:
                    address_data = AddressData(**item)
                    result = await ttl_converter.convert_address_data(address_data)
                    if result.success:
                        logger.info(f"  ✓ Address TTL conversion: {result.triples_count} triples")
                    else:
                        logger.error(f"  ✗ Address TTL conversion failed: {result.error_message}")
                
            except Exception as e:
                logger.error(f"  ✗ Error processing item: {e}")
        
        logger.info("Sample data processing test completed!")
        
    except Exception as e:
        logger.error(f"Sample data test failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if deps:
            from core.dependencies import cleanup_dependencies
            await cleanup_dependencies()


def test_api_models():
    """Test API models and serialization."""
    try:
        logger.info("Testing API models...")
        
        from models.data_models import (
            QueryRequest, QueryResponse, BuildingData, AddressData,
            Session, ConversationTurn, ProcessingTask, DocumentInfo
        )
        from datetime import datetime
        import uuid
        
        # Test QueryRequest
        query_request = QueryRequest(
            query_id=str(uuid.uuid4()),
            query_text="Find all buildings in Berlin",
            query_type="natural_language"
        )
        logger.info("✓ QueryRequest model works")
        
        # Test QueryResponse
        query_response = QueryResponse(
            query_id=query_request.query_id,
            response_text="Found 5 buildings in Berlin",
            processing_time=1.23
        )
        logger.info("✓ QueryResponse model works")
        
        # Test BuildingData
        building_data = BuildingData(
            building_id="test_001",
            name="Test Building",
            coordinates={"lat": 52.5, "lon": 13.4}
        )
        logger.info("✓ BuildingData model works")
        
        # Test AddressData
        address_data = AddressData(
            address_id="addr_001",
            street="Test Street 123",
            city="Berlin",
            postal_code="10115"
        )
        logger.info("✓ AddressData model works")
        
        logger.info("API models test completed successfully!")
        
    except Exception as e:
        logger.error(f"API models test failed: {e}")
        import traceback
        traceback.print_exc()


def main():
    """Run all tests for standalone execution."""
    logger.info("Starting implementation tests...")

    # Run tests
    asyncio.run(test_basic_functionality())
    asyncio.run(test_sample_data_processing())
    test_api_models()

    logger.info("All tests completed!")


if __name__ == "__main__":
    main()
