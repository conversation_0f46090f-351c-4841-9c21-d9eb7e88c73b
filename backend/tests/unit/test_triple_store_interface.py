"""
Unit tests for the triple store interface and RDFLib implementation.
"""
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock

from infrastructure.triple_store_interface import (
    TripleStoreConfig,
    SPARQLResult,
    SPARQLUpdateResult,
    TripleStoreError,
    TripleStoreConnectionError,
    TripleStoreQueryError,
    TripleStoreUpdateError
)
from infrastructure.rdflib_triple_store import RDFLibTripleStore
from infrastructure.graphdb_client_new import GraphDBClient
from config.settings import GraphDBSettings


class TestTripleStoreConfig:
    """Test the TripleStoreConfig dataclass."""
    
    def test_config_creation(self):
        """Test creating a basic configuration."""
        config = TripleStoreConfig(
            query_endpoint="http://localhost:7200/repositories/test",
            update_endpoint="http://localhost:7200/repositories/test/statements",
            default_repository="test"
        )
        
        assert config.query_endpoint == "http://localhost:7200/repositories/test"
        assert config.update_endpoint == "http://localhost:7200/repositories/test/statements"
        assert config.default_repository == "test"
        assert config.has_update_endpoint is True
        assert config.timeout == 30
    
    def test_config_without_update_endpoint(self):
        """Test configuration without update endpoint."""
        config = TripleStoreConfig(
            query_endpoint="http://localhost:7200/repositories/test"
        )
        
        assert config.has_update_endpoint is False
    
    def test_config_with_auth(self):
        """Test configuration with authentication."""
        config = TripleStoreConfig(
            query_endpoint="http://localhost:7200/repositories/test",
            username="admin",
            password="secret"
        )
        
        assert config.username == "admin"
        assert config.password == "secret"


class TestSPARQLResult:
    """Test the SPARQLResult dataclass."""
    
    def test_successful_result(self):
        """Test creating a successful SPARQL result."""
        result = SPARQLResult(
            success=True,
            results=[{"name": "John", "age": "30"}],
            variables=["name", "age"],
            count=1
        )
        
        assert result.success is True
        assert len(result.results) == 1
        assert result.count == 1
        assert result.variables == ["name", "age"]
        assert result.error_message is None
    
    def test_failed_result(self):
        """Test creating a failed SPARQL result."""
        result = SPARQLResult(
            success=False,
            results=[],
            variables=[],
            count=0,
            error_message="Query failed"
        )
        
        assert result.success is False
        assert result.count == 0
        assert result.error_message == "Query failed"


class TestSPARQLUpdateResult:
    """Test the SPARQLUpdateResult dataclass."""
    
    def test_successful_update(self):
        """Test creating a successful update result."""
        result = SPARQLUpdateResult(success=True, affected_triples=5)
        
        assert result.success is True
        assert result.affected_triples == 5
        assert result.error_message is None
    
    def test_failed_update(self):
        """Test creating a failed update result."""
        result = SPARQLUpdateResult(
            success=False,
            error_message="Update failed"
        )
        
        assert result.success is False
        assert result.error_message == "Update failed"


@pytest.mark.asyncio
class TestRDFLibTripleStore:
    """Test the RDFLib triple store implementation."""
    
    @pytest.fixture
    def config(self):
        """Create a test configuration."""
        return TripleStoreConfig(
            query_endpoint="http://localhost:7200/repositories/test",
            update_endpoint="http://localhost:7200/repositories/test/statements",
            default_repository="test"
        )
    
    @pytest.fixture
    def store(self, config):
        """Create a test store instance."""
        return RDFLibTripleStore(config)
    
    async def test_initialization(self, store):
        """Test store initialization."""
        with patch('aiohttp.ClientSession') as mock_session, \
             patch('infrastructure.rdflib_triple_store.SPARQLWrapper') as mock_wrapper, \
             patch.object(store, 'health_check', return_value=True) as mock_health:

            await store.initialize()

            assert store.session is not None
            assert store._query_wrapper is not None
            mock_session.assert_called_once()
            # SPARQLWrapper should be called at least once (query wrapper)
            assert mock_wrapper.call_count >= 1
    
    async def test_close(self, store):
        """Test store closing."""
        mock_session = AsyncMock()
        store.session = mock_session
        
        await store.close()
        
        mock_session.close.assert_called_once()
    
    async def test_health_check_success(self, store):
        """Test successful health check."""
        with patch.object(store, 'execute_sparql_query') as mock_query:
            mock_query.return_value = SPARQLResult(
                success=True, results=[], variables=[], count=0
            )
            
            result = await store.health_check()
            
            assert result is True
            mock_query.assert_called_once_with("ASK { ?s ?p ?o }")
    
    async def test_health_check_failure(self, store):
        """Test failed health check."""
        with patch.object(store, 'execute_sparql_query') as mock_query:
            mock_query.side_effect = Exception("Connection failed")
            
            result = await store.health_check()
            
            assert result is False
    
    async def test_execute_select_query(self, store):
        """Test executing a SELECT query."""
        mock_wrapper = Mock()
        mock_result = {
            'head': {'vars': ['name', 'age']},
            'results': {
                'bindings': [
                    {
                        'name': {'type': 'literal', 'value': 'John'},
                        'age': {'type': 'literal', 'value': '30'}
                    }
                ]
            }
        }
        mock_wrapper.queryAndConvert.return_value = mock_result
        store._query_wrapper = mock_wrapper

        with patch('asyncio.get_event_loop') as mock_loop:
            # Create a mock future that returns the result
            mock_future = asyncio.Future()
            mock_future.set_result(mock_result)
            mock_loop.return_value.run_in_executor.return_value = mock_future

            result = await store.execute_sparql_query("SELECT ?name ?age WHERE { ?s ?p ?o }")

            assert result.success is True
            assert len(result.results) == 1
            assert result.results[0]['name'] == 'John'
            assert result.results[0]['age'] == '30'
            assert result.variables == ['name', 'age']
    
    async def test_execute_ask_query(self, store):
        """Test executing an ASK query."""
        mock_wrapper = Mock()
        mock_wrapper.queryAndConvert.return_value = True
        store._query_wrapper = mock_wrapper

        with patch('asyncio.get_event_loop') as mock_loop:
            # Create a mock future that returns the result
            mock_future = asyncio.Future()
            mock_future.set_result(True)
            mock_loop.return_value.run_in_executor.return_value = mock_future

            result = await store.execute_sparql_query("ASK { ?s ?p ?o }")

            assert result.success is True
            assert result.results[0]['result'] is True
            assert result.variables == ['result']
    
    async def test_execute_sparql_update(self, store):
        """Test executing a SPARQL update."""
        mock_wrapper = Mock()
        mock_wrapper.queryAndConvert.return_value = None
        store._update_wrapper = mock_wrapper

        with patch('asyncio.get_event_loop') as mock_loop:
            # Create a mock future that returns None
            mock_future = asyncio.Future()
            mock_future.set_result(None)
            mock_loop.return_value.run_in_executor.return_value = mock_future

            result = await store.execute_sparql_update("INSERT DATA { <s> <p> <o> }")

            assert result.success is True
            assert result.error_message is None
    
    async def test_upload_ttl_data(self, store):
        """Test uploading TTL data."""
        ttl_content = "@prefix ex: <http://example.org/> . ex:subject ex:predicate ex:object ."

        with patch('infrastructure.rdflib_triple_store.Graph') as mock_graph_class, \
             patch.object(store, 'execute_sparql_update') as mock_update:

            # Mock the Graph instance and its parse method
            mock_graph_instance = Mock()
            mock_graph_class.return_value = mock_graph_instance
            mock_update.return_value = SPARQLUpdateResult(success=True)

            result = await store.upload_ttl_data(ttl_content)

            assert result.success is True
            mock_graph_class.assert_called_once()
            mock_graph_instance.parse.assert_called_once_with(data=ttl_content, format='turtle')
            mock_update.assert_called_once()
    
    async def test_clear_repository(self, store):
        """Test clearing a repository."""
        with patch.object(store, 'execute_sparql_update') as mock_update:
            mock_update.return_value = SPARQLUpdateResult(success=True)
            
            result = await store.clear_repository()
            
            assert result.success is True
            mock_update.assert_called_once_with("DELETE WHERE { ?s ?p ?o }", None)
    
    async def test_get_repository_size(self, store):
        """Test getting repository size."""
        with patch.object(store, 'execute_sparql_query') as mock_query:
            mock_query.return_value = SPARQLResult(
                success=True,
                results=[{'count': '42'}],
                variables=['count'],
                count=1
            )
            
            size = await store.get_repository_size()
            
            assert size == 42
            mock_query.assert_called_once_with("SELECT (COUNT(*) as ?count) WHERE { ?s ?p ?o }", None)
