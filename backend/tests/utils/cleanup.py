"""
Cleanup utilities for test data in MinIO, Qdrant, and GraphDB.
"""
import asyncio
import logging
from typing import List, Optional

from infrastructure.minio_client import MinIOClient
from infrastructure.qdrant_client import QdrantClient
from infrastructure.graphdb_client import GraphDBClient

logger = logging.getLogger(__name__)


class MinIOCleanup:
    """Utility class for cleaning up MinIO test data."""
    
    def __init__(self, minio_client: MinIOClient):
        self.client = minio_client
    
    async def cleanup_test_files(self, 
                               bucket_name: Optional[str] = None,
                               file_extensions: Optional[List[str]] = None) -> bool:
        """
        Clean up test files from MinIO bucket.
        
        Args:
            bucket_name: Bucket to clean up (uses default if None)
            file_extensions: List of extensions to clean (.json, .md, etc.)
                           If None, cleans all non-PDF files
        
        Returns:
            True if cleanup successful, False otherwise
        """
        try:
            if file_extensions is None:
                # Default: clean JSON and markdown files, keep PDFs
                file_extensions = ['.json', '.md', '.txt', '.html']
            
            bucket = bucket_name or self.client.bucket_name
            
            # List all objects in bucket
            objects = await self.client.list_objects()

            deleted_count = 0
            for obj in objects:
                # Check if object has one of the target extensions
                if any(obj.lower().endswith(ext) for ext in file_extensions):
                    try:
                        await self.client.delete_object(obj)
                        deleted_count += 1
                        logger.info(f"Deleted test file: {obj}")
                    except Exception as e:
                        logger.error(f"Failed to delete {obj}: {e}")
            
            logger.info(f"MinIO cleanup completed. Deleted {deleted_count} files.")
            return True
            
        except Exception as e:
            logger.error(f"MinIO cleanup failed: {e}")
            return False
    
    async def cleanup_by_prefix(self, prefix: str, bucket_name: Optional[str] = None) -> bool:
        """
        Clean up files with specific prefix from MinIO bucket.
        
        Args:
            prefix: Prefix to match for deletion
            bucket_name: Bucket to clean up (uses default if None)
        
        Returns:
            True if cleanup successful, False otherwise
        """
        try:
            bucket = bucket_name or self.client.bucket_name
            objects = await self.client.list_objects(prefix=prefix)

            deleted_count = 0
            for obj in objects:
                try:
                    await self.client.delete_object(obj)
                    deleted_count += 1
                    logger.info(f"Deleted test file: {obj}")
                except Exception as e:
                    logger.error(f"Failed to delete {obj}: {e}")
            
            logger.info(f"MinIO prefix cleanup completed. Deleted {deleted_count} files.")
            return True
            
        except Exception as e:
            logger.error(f"MinIO prefix cleanup failed: {e}")
            return False


class QdrantCleanup:
    """Utility class for cleaning up Qdrant test data."""
    
    def __init__(self, qdrant_client: QdrantClient):
        self.client = qdrant_client
    
    async def cleanup_test_collections(self, 
                                     collection_names: Optional[List[str]] = None) -> bool:
        """
        Clean up test collections from Qdrant.
        
        Args:
            collection_names: List of collection names to delete
                            If None, deletes default test collections
        
        Returns:
            True if cleanup successful, False otherwise
        """
        try:
            if collection_names is None:
                # Default test collection names
                collection_names = ['test_documents', 'test_embeddings', 'documents']
            
            deleted_count = 0
            for collection_name in collection_names:
                try:
                    # Check if collection exists before trying to delete
                    loop = asyncio.get_event_loop()
                    collections = await loop.run_in_executor(None, self.client.client.get_collections)
                    if collection_name in [c.name for c in collections.collections]:
                        await loop.run_in_executor(None, self.client.client.delete_collection, collection_name)
                        deleted_count += 1
                        logger.info(f"Deleted test collection: {collection_name}")
                except Exception as e:
                    logger.error(f"Failed to delete collection {collection_name}: {e}")
            
            logger.info(f"Qdrant cleanup completed. Deleted {deleted_count} collections.")
            return True
            
        except Exception as e:
            logger.error(f"Qdrant cleanup failed: {e}")
            return False
    
    async def cleanup_points_by_filter(self, 
                                     collection_name: str,
                                     filter_condition: dict) -> bool:
        """
        Clean up specific points from a Qdrant collection using filters.
        
        Args:
            collection_name: Name of the collection
            filter_condition: Filter condition to match points for deletion
        
        Returns:
            True if cleanup successful, False otherwise
        """
        try:
            # Delete points matching the filter - use the client's delete_points method
            await self.client.delete_points(filter_condition)
            
            logger.info(f"Deleted points from collection {collection_name} with filter: {filter_condition}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete points from {collection_name}: {e}")
            return False
    
    async def recreate_collection(self, collection_name: str, vector_size: int = 384) -> bool:
        """
        Recreate a collection (delete and create new).
        
        Args:
            collection_name: Name of the collection to recreate
            vector_size: Size of vectors in the collection
        
        Returns:
            True if recreation successful, False otherwise
        """
        try:
            # Delete existing collection if it exists
            try:
                loop = asyncio.get_event_loop()
                await loop.run_in_executor(None, self.client.client.delete_collection, collection_name)
                logger.info(f"Deleted existing collection: {collection_name}")
            except Exception:
                # Collection might not exist, which is fine
                pass

            # Create new collection
            from qdrant_client.http.models import Distance, VectorParams
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                self.client.client.create_collection,
                collection_name,
                VectorParams(size=vector_size, distance=Distance.COSINE)
            )
            logger.info(f"Created new collection: {collection_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to recreate collection {collection_name}: {e}")
            return False


class GraphDBCleanup:
    """Utility class for cleaning up GraphDB test data."""
    
    def __init__(self, graphdb_client: GraphDBClient):
        self.client = graphdb_client
    
    async def cleanup_test_repository(self, repository_name: Optional[str] = None) -> bool:
        """
        Clean up test data from GraphDB repository.
        
        Args:
            repository_name: Repository to clean up (uses default if None)
        
        Returns:
            True if cleanup successful, False otherwise
        """
        try:
            repo = repository_name or self.client.repository
            
            # SPARQL query to delete all triples
            delete_query = """
            DELETE WHERE {
                ?s ?p ?o .
            }
            """
            
            result = await self.client.execute_sparql_update(delete_query, repository=repo)
            logger.info(f"GraphDB cleanup completed for repository: {repo}")
            return True
            
        except Exception as e:
            logger.error(f"GraphDB cleanup failed: {e}")
            return False
    
    async def cleanup_by_graph(self, graph_uri: str, repository_name: Optional[str] = None) -> bool:
        """
        Clean up specific named graph from GraphDB.
        
        Args:
            graph_uri: URI of the named graph to clean
            repository_name: Repository to clean up (uses default if None)
        
        Returns:
            True if cleanup successful, False otherwise
        """
        try:
            repo = repository_name or self.client.repository
            
            # SPARQL query to delete all triples from specific graph
            delete_query = f"""
            DELETE WHERE {{
                GRAPH <{graph_uri}> {{
                    ?s ?p ?o .
                }}
            }}
            """
            
            result = await self.client.execute_sparql_update(delete_query, repository=repo)
            logger.info(f"GraphDB graph cleanup completed for: {graph_uri}")
            return True
            
        except Exception as e:
            logger.error(f"GraphDB graph cleanup failed: {e}")
            return False
    
    async def cleanup_by_prefix(self, prefix: str, repository_name: Optional[str] = None) -> bool:
        """
        Clean up triples with subjects/objects matching a prefix.
        
        Args:
            prefix: URI prefix to match for deletion
            repository_name: Repository to clean up (uses default if None)
        
        Returns:
            True if cleanup successful, False otherwise
        """
        try:
            repo = repository_name or self.client.repository
            
            # SPARQL query to delete triples with matching prefix
            delete_query = f"""
            DELETE WHERE {{
                {{
                    ?s ?p ?o .
                    FILTER(STRSTARTS(STR(?s), "{prefix}"))
                }}
                UNION
                {{
                    ?s ?p ?o .
                    FILTER(STRSTARTS(STR(?o), "{prefix}"))
                }}
            }}
            """
            
            result = await self.client.execute_sparql_update(delete_query, repository=repo)
            logger.info(f"GraphDB prefix cleanup completed for: {prefix}")
            return True
            
        except Exception as e:
            logger.error(f"GraphDB prefix cleanup failed: {e}")
            return False


class DataCleanup:
    """Combined cleanup utility for all test data."""
    
    def __init__(self, 
                 minio_client: MinIOClient,
                 qdrant_client: QdrantClient,
                 graphdb_client: GraphDBClient):
        self.minio_cleanup = MinIOCleanup(minio_client)
        self.qdrant_cleanup = QdrantCleanup(qdrant_client)
        self.graphdb_cleanup = GraphDBCleanup(graphdb_client)
    
    async def cleanup_all(self) -> bool:
        """
        Clean up all test data from all systems.
        
        Returns:
            True if all cleanups successful, False otherwise
        """
        results = []
        
        # Clean up MinIO
        logger.info("Starting MinIO cleanup...")
        results.append(await self.minio_cleanup.cleanup_test_files())
        
        # Clean up Qdrant
        logger.info("Starting Qdrant cleanup...")
        results.append(await self.qdrant_cleanup.cleanup_test_collections())
        
        # Clean up GraphDB
        logger.info("Starting GraphDB cleanup...")
        results.append(await self.graphdb_cleanup.cleanup_test_repository())
        
        success = all(results)
        if success:
            logger.info("All test data cleanup completed successfully")
        else:
            logger.error("Some cleanup operations failed")
        
        return success
