"""
Pytest configuration and shared fixtures for the RDF Agent System tests.
"""
import asyncio
import os
import tempfile
from pathlib import Path
from typing import Async<PERSON>enerator, Generator
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
import pytest_asyncio
from pydantic_ai.models.test import TestModel

from core.dependencies import Dependencies, get_dependencies
from config.settings import Settings
from infrastructure.minio_client import MinIOClient
from infrastructure.qdrant_client import QdrantClient
from infrastructure.graphdb_client import GraphDBClient
from infrastructure.kafka_client import KafkaClient

# Set fake OpenAI API key for testing
os.environ["OPENAI_API_KEY"] = "fake-test-key-for-testing"


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def test_settings() -> Settings:
    """Create test settings with mock values."""
    return Settings(
        kafka={"bootstrap_servers": "localhost:9092", "group_id": "test-group"},
        graphdb={"url": "http://localhost:7200", "default_repository": "test"},
        minio={"endpoint": "localhost:9000", "bucket_name": "test-bucket"},
        qdrant={"host": "localhost", "port": 6333},
        docling={"enabled": True, "output_format": "markdown", "timeout": 300},
        database={"url": "sqlite:///:memory:"},
        ai={"model": "test:test", "api_key": "test-key", "or_api_key": "test-or-key"}
    )


@pytest.fixture
def mock_minio_client() -> AsyncMock:
    """Create a mock MinIO client."""
    mock_client = AsyncMock(spec=MinIOClient)
    mock_client.list_objects.return_value = []
    mock_client.download_data.return_value = b"test content"
    mock_client.upload_data.return_value = True
    mock_client.upload_file.return_value = True
    mock_client.delete_object.return_value = True

    return mock_client


@pytest.fixture
def mock_qdrant_client() -> AsyncMock:
    """Create a mock Qdrant client."""
    mock_client = AsyncMock(spec=QdrantClient)
    mock_client.count_points.return_value = 0
    mock_client.search.return_value = []
    mock_client.upsert_points.return_value = True


    return mock_client


@pytest.fixture
def mock_graphdb_client() -> AsyncMock:
    """Create a mock GraphDB client."""
    mock_client = AsyncMock(spec=GraphDBClient)
    mock_client.execute_sparql_query.return_value = {"results": {"bindings": []}}
    mock_client.upload_ttl_data.return_value = True
    mock_client.health_check.return_value = True
    return mock_client


@pytest.fixture
def mock_kafka_client() -> AsyncMock:
    """Create a mock Kafka client."""
    mock_client = AsyncMock(spec=KafkaClient)
    mock_client.produce_message.return_value = True
    mock_client.send_kafka_message.return_value = True
    return mock_client


@pytest_asyncio.fixture
async def mock_dependencies(
    test_settings: Settings,
    mock_minio_client: AsyncMock,
    mock_qdrant_client: AsyncMock,
    mock_graphdb_client: AsyncMock,
    mock_kafka_client: AsyncMock
) -> Dependencies:
    """Create mock dependencies for testing."""
    deps = Dependencies(test_settings)
    deps.settings = test_settings
    deps.minio_client = mock_minio_client
    deps.qdrant_client = mock_qdrant_client
    deps.graphdb_client = mock_graphdb_client
    deps.kafka_client = mock_kafka_client
    return deps


@pytest.fixture
def test_model() -> TestModel:
    """Create a test model for AI agent testing."""
    return TestModel()


@pytest.fixture
def temp_dir() -> Generator[Path, None, None]:
    """Create a temporary directory for test files."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def sample_pdf_path(temp_dir: Path) -> Path:
    """Create a sample PDF file for testing."""
    pdf_path = temp_dir / "sample.pdf"
    # Create a minimal PDF content for testing
    pdf_content = b"""%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj
4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
72 720 Td
(Test PDF content) Tj
ET
endstream
endobj
xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
299
%%EOF"""
    pdf_path.write_bytes(pdf_content)
    return pdf_path


@pytest.fixture
def sample_ttl_content() -> str:
    """Sample TTL content for testing."""
    return """
@prefix ex: <http://example.org/> .
@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .
@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .

ex:Building1 rdf:type ex:Building ;
    rdfs:label "Test Building" ;
    ex:hasAddress ex:Address1 ;
    ex:energyRating "A+" .

ex:Address1 rdf:type ex:Address ;
    ex:street "123 Test Street" ;
    ex:city "Test City" ;
    ex:country "Test Country" .
"""


@pytest.fixture
def sample_ttl_path(temp_dir: Path, sample_ttl_content: str) -> Path:
    """Create a sample TTL file for testing."""
    ttl_path = temp_dir / "sample.ttl"
    ttl_path.write_text(sample_ttl_content)
    return ttl_path


@pytest.fixture
def sample_json_data() -> dict:
    """Sample JSON data for testing."""
    return {
        "id": "test-doc-1",
        "title": "Test Document",
        "content": "This is a test document for the RDF Agent System.",
        "metadata": {
            "author": "Test Author",
            "created": "2024-01-01T00:00:00Z",
            "type": "document"
        }
    }


@pytest.fixture
def sample_markdown_content() -> str:
    """Sample markdown content for testing."""
    return """# Test Document

This is a test document for the RDF Agent System.

## Section 1

Some content here.

## Section 2

More content here.
"""


# Agent fixtures
@pytest_asyncio.fixture
async def orchestrator_agent(mock_dependencies):
    """Create orchestrator agent for testing."""
    with patch('pydantic_ai.Agent') as mock_agent_class:
        # Create a test model
        from tests.utils.helpers import TestModelBuilder
        test_model = TestModelBuilder.create_test_model_with_responses([
            "Test orchestrator response"
        ])

        # Mock the Agent constructor to return a mock agent
        mock_agent = AsyncMock()
        mock_agent.run_sync = AsyncMock(return_value=AsyncMock(data="Test orchestrator response"))
        mock_agent_class.return_value = mock_agent

        from agents.orchestrator_agent import OrchestratorAgent
        agent = OrchestratorAgent(mock_dependencies)
        agent.agent = mock_agent  # Override with our mock
        return agent


@pytest_asyncio.fixture
async def rag_agent(mock_dependencies):
    """Create RAG agent for testing."""
    with patch('pydantic_ai.Agent') as mock_agent_class, \
         patch('agents.rag_agent.SentenceTransformer'):
        # Create a test model
        from tests.utils.helpers import TestModelBuilder
        test_model = TestModelBuilder.create_test_model_with_responses([
            "Test RAG response"
        ])

        # Mock the Agent constructor to return a mock agent
        mock_agent = AsyncMock()
        mock_agent.run_sync = AsyncMock(return_value=AsyncMock(data="Test RAG response"))
        mock_agent_class.return_value = mock_agent

        from agents.rag_agent import RAGAgent
        agent = RAGAgent(mock_dependencies)
        agent.agent = mock_agent  # Override with our mock
        return agent


@pytest_asyncio.fixture
async def rdf_agent(mock_dependencies):
    """Create RDF query agent for testing."""
    with patch('pydantic_ai.Agent') as mock_agent_class:
        # Create a test model
        from tests.utils.helpers import TestModelBuilder
        test_model = TestModelBuilder.create_test_model_with_responses([
            "Test RDF response"
        ])

        # Mock the Agent constructor to return a mock agent
        mock_agent = AsyncMock()
        mock_agent.run_sync = AsyncMock(return_value=AsyncMock(data="Test RDF response"))
        mock_agent_class.return_value = mock_agent

        from agents.rdf_query_agent import RDFQueryAgent
        agent = RDFQueryAgent(mock_dependencies)
        agent.agent = mock_agent  # Override with our mock
        return agent


# Cleanup fixtures for integration tests
@pytest.fixture
async def cleanup_minio():
    """Cleanup MinIO test data after tests."""
    yield
    # Cleanup logic will be implemented in the cleanup utilities


@pytest.fixture
async def cleanup_qdrant():
    """Cleanup Qdrant test data after tests."""
    yield
    # Cleanup logic will be implemented in the cleanup utilities


@pytest.fixture
async def cleanup_graphdb():
    """Cleanup GraphDB test data after tests."""
    yield
    # Cleanup logic will be implemented in the cleanup utilities


# Pytest configuration
def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line("markers", "unit: mark test as a unit test")
    config.addinivalue_line("markers", "integration: mark test as an integration test")
    config.addinivalue_line("markers", "e2e: mark test as an end-to-end test")
    config.addinivalue_line("markers", "slow: mark test as slow running")


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers based on test location."""
    for item in items:
        # Add markers based on test file location
        if "unit" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        elif "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        elif "e2e" in str(item.fspath):
            item.add_marker(pytest.mark.e2e)
