"""
Integration tests for API endpoints.
"""
import asyncio
import json
import httpx
import pytest


@pytest.mark.asyncio
async def test_api_endpoints():
    """Test basic API endpoints."""
    base_url = "http://127.0.0.1:8001"
    
    async with httpx.AsyncClient() as client:
        print("Testing API endpoints...")
        
        # Test health endpoint
        try:
            response = await client.get(f"{base_url}/health")
            print(f"✓ Health check: {response.status_code} - {response.text}")
        except Exception as e:
            print(f"✗ Health check failed: {e}")
        
        # Test admin status endpoint
        try:
            response = await client.get(f"{base_url}/api/v1/admin/status")
            if response.status_code == 200:
                data = response.json()
                print(f"✓ Admin status: {response.status_code}")
                print(f"  Services: {list(data.get('services', {}).keys())}")
            else:
                print(f"✗ Admin status: {response.status_code} - {response.text}")
        except Exception as e:
            print(f"✗ Admin status failed: {e}")
        
        # Test session creation
        try:
            session_data = {"metadata": {"test": True}}
            response = await client.post(f"{base_url}/api/v1/sessions/", json=session_data)
            if response.status_code == 200:
                session_data = response.json()
                session_id = session_data.get("session_id")
                print(f"✓ Session creation: {response.status_code}")
                print(f"  Session ID: {session_id}")
                
                # Test session retrieval
                if session_id:
                    response = await client.get(f"{base_url}/api/v1/sessions/{session_id}")
                    if response.status_code == 200:
                        print(f"✓ Session retrieval: {response.status_code}")
                    else:
                        print(f"✗ Session retrieval: {response.status_code}")
            else:
                print(f"✗ Session creation: {response.status_code} - {response.text}")
        except Exception as e:
            print(f"✗ Session creation failed: {e}")
        
        # Test document collection stats
        try:
            response = await client.get(f"{base_url}/api/v1/documents/collection/stats")
            if response.status_code == 200:
                stats = response.json()
                print(f"✓ Document stats: {response.status_code}")
                print(f"  Document count: {stats.get('document_count', 0)}")
            else:
                print(f"✗ Document stats: {response.status_code} - {response.text}")
        except Exception as e:
            print(f"✗ Document stats failed: {e}")
        
        print("\nAPI test completed!")


if __name__ == "__main__":
    pytest.main([__file__])
