#!/usr/bin/env python3
"""
Integration tests for Kafka message processing with example data.
"""
import asyncio
import json
import logging
from pathlib import Path
from typing import List, Dict, Any
import pytest

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@pytest.mark.asyncio
async def test_kafka_producer():
    """Test Kafka producer with example data."""
    try:
        from infrastructure.kafka_client import KafkaClient
        from config.settings import Settings
        
        logger.info("Testing Kafka producer...")
        
        # Initialize Kafka client
        settings = Settings()
        kafka_client = KafkaClient(settings.kafka)
        await kafka_client.initialize()
        
        # Load example data
        example_file = Path("../.context/example_files/example_input.json")
        if not example_file.exists():
            logger.error("Example data file not found")
            return False
        
        with open(example_file, 'r') as f:
            example_data = json.load(f)
        
        logger.info(f"Loaded {len(example_data)} example items")
        
        # Test with first 3 items
        topic = "usecases.sample_input"
        success_count = 0
        
        for i, item in enumerate(example_data[:3]):
            try:
                logger.info(f"Sending item {i+1}...")
                await kafka_client.produce_message(topic, item)
                success_count += 1
                logger.info(f"✓ Item {i+1} sent successfully")
            except Exception as e:
                logger.error(f"✗ Failed to send item {i+1}: {e}")
        
        await kafka_client.close()
        
        logger.info(f"✓ Kafka producer test completed: {success_count}/3 messages sent")
        return success_count == 3
        
    except Exception as e:
        logger.error(f"Kafka producer test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


@pytest.mark.asyncio
async def test_kafka_consumer():
    """Test Kafka consumer functionality."""
    try:
        from infrastructure.kafka_client import KafkaClient
        from config.settings import Settings
        
        logger.info("Testing Kafka consumer...")
        
        # Initialize Kafka client
        settings = Settings()
        kafka_client = KafkaClient(settings.kafka)
        await kafka_client.initialize()
        
        # Test consumer setup
        topic = "usecases.sample_input"
        messages_received = []
        
        async def message_handler(message: Dict[str, Any], topic: str):
            """Handle received messages."""
            logger.info(f"Received message from topic {topic}")
            messages_received.append(message)
        
        # Subscribe to topic
        await kafka_client.subscribe_to_topics([topic])
        
        # Consume messages for a short time
        logger.info("Consuming messages for 5 seconds...")
        try:
            await asyncio.wait_for(
                kafka_client.start_consuming(),
                timeout=5.0
            )
        except asyncio.TimeoutError:
            logger.info("Consumer timeout reached")
        
        await kafka_client.close()
        
        logger.info(f"✓ Kafka consumer test completed: {len(messages_received)} messages received")
        return True
        
    except Exception as e:
        logger.error(f"Kafka consumer test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


@pytest.mark.asyncio
async def test_kafka_message_processing():
    """Test the full Kafka message processing pipeline with new usecase format."""
    try:
        from services.kafka_processor import KafkaProcessor
        from core.dependencies import get_dependencies, cleanup_dependencies

        logger.info("Testing Kafka message processing pipeline...")

        # Initialize dependencies
        deps = await get_dependencies()

        # Create Kafka processor
        processor = KafkaProcessor(deps)

        # Create a sample usecase message in the new format
        test_item = {
            "graphTemplate": "@prefix ex: <http://example.org/> .\\nNode1 hasRelation Node2 .",
            "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .\\n@prefix ex: <http://example.org/> .\\nex:TestBuilding rdf:type ex:Building .",
            "accessRights": None,
            "useCase": "TestCase",
            "graphMetadata": [
                {
                    "id": "test-building-001",
                    "classType": "https://ibpdi.datacat.org/class/Building",
                    "propertiesValues": {
                        "Name": "Test Building",
                        "BuildingCode": "TB001",
                        "EnergyEfficiencyClass": "A"
                    }
                }
            ]
        }

        topic = "usecases.test_case"

        logger.info("Processing test message...")
        # Process the usecase message
        await processor._handle_usecase_message(topic, test_item)

        await cleanup_dependencies()

        logger.info("✓ Kafka message processing test completed")
        return True

    except Exception as e:
        logger.error(f"Kafka message processing test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


@pytest.mark.asyncio
async def test_ttl_conversion_with_usecase_data():
    """Test TTL conversion with new usecase message format."""
    try:
        from services.ttl_converter import TTLConverter
        from models.data_models import GraphMetadataItem

        logger.info("Testing TTL conversion with usecase data...")

        ttl_converter = TTLConverter()
        success_count = 0

        # Test metadata conversion
        test_metadata_items = [
            {
                "id": "test-building-001",
                "classType": "https://ibpdi.datacat.org/class/Building",
                "propertiesValues": {
                    "Name": "Test Building 1",
                    "BuildingCode": "TB001",
                    "EnergyEfficiencyClass": "A"
                }
            },
            {
                "id": "test-address-001",
                "classType": "https://ibpdi.datacat.org/class/Address",
                "propertiesValues": {
                    "StreetName": "Test Street",
                    "HouseNumber": "123",
                    "City": "Test City",
                    "PostalCode": "12345"
                }
            }
        ]

        for i, item_data in enumerate(test_metadata_items):
            try:
                logger.info(f"Converting metadata item {i+1} to TTL...")

                # Create metadata item
                metadata_item = GraphMetadataItem(**item_data)

                # Convert to TTL
                result = await ttl_converter.convert_metadata_to_ttl("TestUseCase", metadata_item)

                if result.success:
                    logger.info(f"✓ Item {i+1}: {result.triples_count} triples generated")
                    success_count += 1
                else:
                    logger.error(f"✗ Item {i+1} conversion failed: {result.error_message}")

            except Exception as e:
                logger.error(f"✗ Error converting item {i+1}: {e}")

        logger.info(f"✓ TTL conversion test completed: {success_count}/{len(test_metadata_items)} items converted")
        return success_count > 0

    except Exception as e:
        logger.error(f"TTL conversion test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


@pytest.mark.asyncio
async def test_all_kafka():
    """Run all Kafka tests."""
    logger.info("Starting Kafka tests...")

    # Test TTL conversion first
    await test_ttl_conversion_with_usecase_data()

    # Test Kafka producer
    await test_kafka_producer()

    # Test Kafka consumer
    await test_kafka_consumer()

    # Test message processing pipeline
    await test_kafka_message_processing()

    logger.info("✓ All Kafka tests passed!")


async def main():
    """Run all Kafka tests for standalone execution."""
    logger.info("Starting Kafka tests...")

    results = []

    # Test TTL conversion first
    results.append(await test_ttl_conversion_with_usecase_data())

    # Test Kafka producer
    results.append(await test_kafka_producer())

    # Test Kafka consumer
    results.append(await test_kafka_consumer())

    # Test message processing pipeline
    results.append(await test_kafka_message_processing())

    if all(results):
        logger.info("✓ All Kafka tests passed!")
        return True
    else:
        logger.error("✗ Some Kafka tests failed!")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
