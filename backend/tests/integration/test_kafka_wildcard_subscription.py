"""
Integration tests for Kafka wildcard topic subscription.
Tests that the Kafka processor correctly subscribes to and processes messages from topics matching regex patterns.
"""
import pytest
import asyncio
import json
import logging
from typing import Dict, Any
from unittest.mock import AsyncMock, MagicMock

from core.dependencies import get_dependencies, cleanup_dependencies
from services.kafka_processor import KafkaProcessor
from infrastructure.kafka_client import KafkaClient
from config.settings import Settings, KafkaSettings
from models.data_models import UseCaseMessage, GraphMetadataItem

logger = logging.getLogger(__name__)


@pytest.fixture
def sample_usecase_message():
    """Sample usecase message for testing."""
    return {
        "useCase": "TestWildcardCase",
        "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .\n@prefix ex: <http://example.org/> .\nex:TestBuilding rdf:type ex:Building .",
        "graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .",
        "accessRights": None,
        "graphMetadata": [
            {
                "id": "test-building-wildcard-001",
                "classType": "https://ibpdi.datacat.org/class/Building",
                "propertiesValues": {
                    "Name": "Wildcard Test Building",
                    "BuildingCode": "WTB001"
                }
            }
        ]
    }


@pytest.mark.asyncio
async def test_kafka_client_pattern_subscription():
    """Test that KafkaClient can subscribe to topics using regex patterns."""
    try:
        logger.info("Testing Kafka client pattern subscription...")
        
        # Create mock consumer
        mock_consumer = AsyncMock()
        
        # Initialize Kafka client with mock
        settings = KafkaSettings()
        kafka_client = KafkaClient(settings)
        kafka_client.consumer = mock_consumer
        
        # Test pattern subscription
        pattern = r"^usecases\..*$"
        await kafka_client.subscribe_to_pattern(pattern)
        
        # Verify the consumer.subscribe was called with pattern
        mock_consumer.subscribe.assert_called_once_with(pattern=pattern)
        
        logger.info("✓ Kafka client pattern subscription test passed")
        return True
        
    except Exception as e:
        logger.error(f"Kafka client pattern subscription test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


@pytest.mark.asyncio
async def test_topic_pattern_conversion():
    """Test conversion of shell-style wildcards to regex patterns."""
    try:
        logger.info("Testing topic pattern conversion...")
        
        test_cases = [
            ("usecases.*", r"^usecases\..*$"),
            ("data.usecases.*", r"^data\.usecases\..*$"),
            ("*.usecases.*", r"^.*\.usecases\..*$"),
            ("test.topic.specific", r"^test\.topic\.specific$")
        ]
        
        for shell_pattern, expected_regex in test_cases:
            # Convert shell-style wildcard to regex pattern
            regex_pattern = f"^{shell_pattern.replace('.', r'\.').replace('*', '.*')}$"
            
            assert regex_pattern == expected_regex, f"Pattern conversion failed: {shell_pattern} -> {regex_pattern} != {expected_regex}"
            logger.info(f"✓ Pattern conversion: {shell_pattern} -> {regex_pattern}")
        
        logger.info("✓ Topic pattern conversion test passed")
        return True
        
    except Exception as e:
        logger.error(f"Topic pattern conversion test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


@pytest.mark.asyncio
async def test_message_handler_matching():
    """Test that message handlers correctly match topics."""
    try:
        logger.info("Testing message handler matching...")
        
        # Create mock consumer
        mock_consumer = AsyncMock()
        
        # Initialize Kafka client
        settings = KafkaSettings()
        kafka_client = KafkaClient(settings)
        kafka_client.consumer = mock_consumer
        
        # Register handler
        handler_called = False
        received_topic = None
        received_message = None
        
        async def test_handler(topic: str, message: Dict[str, Any]):
            nonlocal handler_called, received_topic, received_message
            handler_called = True
            received_topic = topic
            received_message = message
        
        kafka_client.register_handler("usecases", test_handler)
        
        # Create mock message
        mock_message = MagicMock()
        mock_message.topic = "usecases.portfolio_example"
        mock_message.value = {"test": "data"}
        
        # Process the message
        await kafka_client._process_message(mock_message)
        
        # Verify handler was called
        assert handler_called, "Handler was not called"
        assert received_topic == "usecases.portfolio_example", f"Wrong topic: {received_topic}"
        assert received_message == {"test": "data"}, f"Wrong message: {received_message}"
        
        logger.info("✓ Message handler matching test passed")
        return True
        
    except Exception as e:
        logger.error(f"Message handler matching test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


@pytest.mark.asyncio
async def test_kafka_processor_pattern_usage():
    """Test that KafkaProcessor uses the configured pattern correctly."""
    try:
        logger.info("Testing Kafka processor pattern usage...")
        
        # Create mock dependencies
        mock_deps = MagicMock()
        mock_kafka_client = AsyncMock()
        mock_settings = MagicMock()
        mock_kafka_settings = MagicMock()
        
        # Configure mock settings
        mock_kafka_settings.usecase_topic_pattern = "data.usecases.*"
        mock_settings.kafka = mock_kafka_settings
        mock_deps.settings = mock_settings
        mock_deps.kafka_client = mock_kafka_client
        
        # Create processor
        processor = KafkaProcessor(mock_deps)
        
        # Start processor (this should call subscribe_to_pattern)
        await processor.start()
        
        # Verify the correct pattern was used
        expected_regex = r"^data\.usecases\..*$"
        mock_kafka_client.subscribe_to_pattern.assert_called_once_with(expected_regex)
        
        logger.info("✓ Kafka processor pattern usage test passed")
        return True
        
    except Exception as e:
        logger.error(f"Kafka processor pattern usage test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


@pytest.mark.asyncio
async def test_wildcard_topic_matching():
    """Test that various topic names match the wildcard patterns correctly."""
    try:
        logger.info("Testing wildcard topic matching...")
        
        import re
        
        # Test different patterns and topics
        test_cases = [
            (r"^usecases\..*$", "usecases.portfolio", True),
            (r"^usecases\..*$", "usecases.building_data", True),
            (r"^usecases\..*$", "usecases.test.nested", True),
            (r"^usecases\..*$", "other.usecases.test", False),
            (r"^usecases\..*$", "usecases", False),  # No dot after usecases
            (r"^data\.usecases\..*$", "data.usecases.portfolio", True),
            (r"^data\.usecases\..*$", "data.usecases.building.test", True),
            (r"^data\.usecases\..*$", "usecases.portfolio", False),
            (r"^data\.usecases\..*$", "data.other.portfolio", False),
        ]
        
        for pattern, topic, should_match in test_cases:
            matches = bool(re.match(pattern, topic))
            assert matches == should_match, f"Pattern {pattern} vs topic {topic}: expected {should_match}, got {matches}"
            logger.info(f"✓ Pattern {pattern} vs topic {topic}: {matches} (expected {should_match})")
        
        logger.info("✓ Wildcard topic matching test passed")
        return True
        
    except Exception as e:
        logger.error(f"Wildcard topic matching test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


@pytest.mark.asyncio
async def test_all_wildcard_subscription():
    """Run all wildcard subscription tests."""
    logger.info("Starting wildcard subscription tests...")

    results = []

    # Test pattern subscription
    results.append(await test_kafka_client_pattern_subscription())

    # Test pattern conversion
    results.append(await test_topic_pattern_conversion())

    # Test message handler matching
    results.append(await test_message_handler_matching())

    # Test processor pattern usage
    results.append(await test_kafka_processor_pattern_usage())

    # Test wildcard topic matching
    results.append(await test_wildcard_topic_matching())

    if all(results):
        logger.info("✓ All wildcard subscription tests passed!")
        return True
    else:
        logger.error("✗ Some wildcard subscription tests failed!")
        return False


if __name__ == "__main__":
    success = asyncio.run(test_all_wildcard_subscription())
    exit(0 if success else 1)
