"""
Integration tests for document processing pipeline.
Tests the complete flow: MinIO -> Docling -> Qdrant
"""
import pytest
import asyncio
from pathlib import Path
from typing import List, Dict, Any

from core.dependencies import get_dependencies, cleanup_dependencies
from services.docling_service import DoclingService
from services.kafka_processor import KafkaProcessor
from tests.utils.cleanup import DataCleanup
from tests.utils.helpers import TestDataGenerator, FileHelpers, AsyncTestHelpers


@pytest.mark.integration
@pytest.mark.requires_services
class TestDocumentProcessingIntegration:
    """Integration tests for the complete document processing pipeline."""
    
    @pytest.fixture(scope="class")
    async def dependencies(self):
        """Get real dependencies for integration testing."""
        deps = await get_dependencies()
        yield deps
        await cleanup_dependencies()
    
    @pytest.fixture(scope="class")
    async def cleanup_util(self, dependencies):
        """Create cleanup utility for test data."""
        return DataCleanup(
            dependencies.minio_client,
            dependencies.qdrant_client,
            dependencies.graphdb_client
        )
    
    @pytest.fixture(autouse=True)
    async def setup_and_cleanup(self, cleanup_util):
        """Setup and cleanup test data before and after each test."""
        # Cleanup before test
        await cleanup_util.cleanup_all()
        yield
        # Cleanup after test
        await cleanup_util.cleanup_all()
    
    @pytest.fixture
    def sample_documents(self, temp_dir: Path) -> List[Path]:
        """Create sample documents for testing."""
        documents = []
        
        # Create a sample PDF
        pdf_path = temp_dir / "test_document.pdf"
        FileHelpers.create_test_pdf(
            pdf_path, 
            "This is a test document about energy efficiency in buildings. "
            "LED lighting can reduce energy consumption by 40%. "
            "Proper insulation is crucial for maintaining temperature."
        )
        documents.append(pdf_path)
        
        # Create another PDF
        pdf_path2 = temp_dir / "sustainability_report.pdf"
        FileHelpers.create_test_pdf(
            pdf_path2,
            "Sustainability report on renewable energy sources. "
            "Solar panels and wind turbines are becoming more efficient. "
            "Green building practices reduce environmental impact."
        )
        documents.append(pdf_path2)
        
        return documents
    
    @pytest.mark.asyncio
    async def test_complete_document_processing_pipeline(
        self, 
        dependencies, 
        sample_documents: List[Path]
    ):
        """Test the complete document processing pipeline."""
        docling_service = DoclingService(dependencies.settings.docling)
        
        processed_docs = []
        
        for doc_path in sample_documents:
            # Step 1: Upload document to MinIO
            filename = doc_path.name
            with open(doc_path, 'rb') as f:
                content = f.read()
            
            upload_success = await dependencies.minio_client.put_object(
                filename, content
            )
            assert upload_success, f"Failed to upload {filename} to MinIO"
            
            # Step 2: Process document with Docling
            processing_result = await docling_service.process_document(filename)
            assert processing_result is not None
            assert processing_result.get("success", False)
            
            # Step 3: Verify JSON and Markdown files were created
            json_filename = filename.replace('.pdf', '.json')
            md_filename = filename.replace('.pdf', '.md')
            
            # Check if processed files exist in MinIO
            objects = await dependencies.minio_client.list_objects()
            assert json_filename in objects, f"JSON file {json_filename} not found in MinIO"
            assert md_filename in objects, f"Markdown file {md_filename} not found in MinIO"
            
            # Step 4: Verify content was extracted
            json_content = await dependencies.minio_client.get_object(json_filename)
            assert json_content is not None
            assert len(json_content) > 0
            
            md_content = await dependencies.minio_client.get_object(md_filename)
            assert md_content is not None
            assert len(md_content) > 0
            
            processed_docs.append({
                "filename": filename,
                "json_file": json_filename,
                "md_file": md_filename,
                "processing_result": processing_result
            })
        
        # Step 5: Verify embeddings were created in Qdrant
        # Wait a bit for async processing to complete
        await asyncio.sleep(2)
        
        point_count = await dependencies.qdrant_client.count_points()
        assert point_count > 0, "No embeddings found in Qdrant after processing"
        
        # Step 6: Test search functionality
        search_results = await dependencies.qdrant_client.search(
            query_vector=[0.1] * 384,  # Mock query vector
            limit=5
        )
        assert len(search_results) > 0, "No search results found"
        
        return processed_docs
    
    @pytest.mark.asyncio
    async def test_document_processing_with_kafka(
        self, 
        dependencies, 
        sample_documents: List[Path]
    ):
        """Test document processing triggered by Kafka messages."""
        kafka_processor = KafkaProcessor(dependencies)
        
        for doc_path in sample_documents:
            # Upload document to MinIO first
            filename = doc_path.name
            with open(doc_path, 'rb') as f:
                content = f.read()
            
            await dependencies.minio_client.put_object(filename, content)
            
            # Create Kafka message
            kafka_message = TestDataGenerator.generate_kafka_message("document_processing")
            kafka_message["data"]["filename"] = filename
            
            # Process the message
            processing_result = await kafka_processor.process_message(kafka_message)
            assert processing_result is not None
            
            # Verify processing completed
            await AsyncTestHelpers.wait_for_condition(
                lambda: self._check_processing_complete(dependencies, filename),
                timeout=30.0
            )
    
    async def _check_processing_complete(self, dependencies, filename: str) -> bool:
        """Check if document processing is complete."""
        try:
            objects = await dependencies.minio_client.list_objects()
            json_file = filename.replace('.pdf', '.json')
            md_file = filename.replace('.pdf', '.md')
            return json_file in objects and md_file in objects
        except Exception:
            return False
    
    @pytest.mark.asyncio
    async def test_document_processing_error_handling(
        self, 
        dependencies, 
        temp_dir: Path
    ):
        """Test error handling in document processing."""
        docling_service = DoclingService(dependencies.settings.docling)

        # Create an invalid/corrupted PDF
        invalid_pdf = temp_dir / "invalid.pdf"
        invalid_pdf.write_text("This is not a valid PDF file")
        
        # Upload the invalid file
        with open(invalid_pdf, 'rb') as f:
            content = f.read()
        
        await dependencies.minio_client.put_object("invalid.pdf", content)
        
        # Try to process it
        result = await docling_service.process_document("invalid.pdf")
        
        # Should handle the error gracefully
        assert result is not None
        assert result.get("success", True) is False or "error" in result
    
    @pytest.mark.asyncio
    async def test_large_document_processing(
        self, 
        dependencies, 
        temp_dir: Path
    ):
        """Test processing of larger documents."""
        # Create a larger test document
        large_content = "Energy efficiency in buildings. " * 1000  # Repeat to make it larger
        large_pdf = temp_dir / "large_document.pdf"
        FileHelpers.create_test_pdf(large_pdf, large_content)
        
        # Upload and process
        with open(large_pdf, 'rb') as f:
            content = f.read()
        
        await dependencies.minio_client.put_object("large_document.pdf", content)
        
        docling_service = DoclingService(dependencies.settings.docling)
        result = await docling_service.process_document("large_document.pdf")
        
        assert result is not None
        assert result.get("success", False)
        
        # Verify chunking worked properly
        point_count = await dependencies.qdrant_client.count_points()
        assert point_count > 1, "Large document should be split into multiple chunks"
    
    @pytest.mark.asyncio
    async def test_concurrent_document_processing(
        self, 
        dependencies, 
        sample_documents: List[Path]
    ):
        """Test concurrent processing of multiple documents."""
        docling_service = DoclingService(dependencies.settings.docling)

        # Upload all documents first
        upload_tasks = []
        for doc_path in sample_documents:
            filename = doc_path.name
            with open(doc_path, 'rb') as f:
                content = f.read()
            upload_tasks.append(
                dependencies.minio_client.put_object(filename, content)
            )
        
        upload_results = await asyncio.gather(*upload_tasks)
        assert all(upload_results), "Some uploads failed"
        
        # Process all documents concurrently
        processing_tasks = [
            docling_service.process_document(doc.name) 
            for doc in sample_documents
        ]
        
        processing_results = await asyncio.gather(*processing_tasks, return_exceptions=True)
        
        # Check that all processing completed successfully
        for i, result in enumerate(processing_results):
            if isinstance(result, Exception):
                pytest.fail(f"Processing failed for {sample_documents[i].name}: {result}")
            assert result is not None
            assert result.get("success", False)
    
    @pytest.mark.asyncio
    async def test_document_metadata_preservation(
        self, 
        dependencies, 
        sample_documents: List[Path]
    ):
        """Test that document metadata is preserved through processing."""
        docling_service = DoclingService(dependencies.settings.docling)

        for doc_path in sample_documents:
            filename = doc_path.name
            
            # Upload with metadata
            with open(doc_path, 'rb') as f:
                content = f.read()
            
            await dependencies.minio_client.put_object(
                filename, 
                content,
                metadata={
                    "author": "Test Author",
                    "category": "energy_efficiency",
                    "upload_time": "2024-01-01T00:00:00Z"
                }
            )
            
            # Process document
            result = await docling_service.process_document(filename)
            assert result is not None
            
            # Check that metadata is preserved in embeddings
            search_results = await dependencies.qdrant_client.search(
                query_vector=[0.1] * 384,
                limit=10
            )
            
            # Find results from this document
            doc_results = [
                r for r in search_results 
                if r.get("payload", {}).get("filename") == filename
            ]
            
            assert len(doc_results) > 0, f"No embeddings found for {filename}"
            
            # Check metadata preservation
            for result in doc_results:
                payload = result.get("payload", {})
                assert "filename" in payload
                # Additional metadata checks can be added here
    
    @pytest.mark.asyncio
    async def test_document_versioning(
        self, 
        dependencies, 
        temp_dir: Path
    ):
        """Test handling of document versions/updates."""
        docling_service = DoclingService(dependencies.settings.docling)
        filename = "versioned_document.pdf"
        
        # Create and process first version
        v1_content = "Version 1: Basic energy efficiency information."
        v1_pdf = temp_dir / "v1.pdf"
        FileHelpers.create_test_pdf(v1_pdf, v1_content)
        
        with open(v1_pdf, 'rb') as f:
            content = f.read()
        
        await dependencies.minio_client.put_object(filename, content)
        result1 = await docling_service.process_document(filename)
        assert result1 is not None
        
        # Wait for processing
        await asyncio.sleep(2)
        count_after_v1 = await dependencies.qdrant_client.count_points()
        
        # Create and process second version
        v2_content = "Version 2: Advanced energy efficiency with solar panels and LED lighting."
        v2_pdf = temp_dir / "v2.pdf"
        FileHelpers.create_test_pdf(v2_pdf, v2_content)
        
        with open(v2_pdf, 'rb') as f:
            content = f.read()
        
        await dependencies.minio_client.put_object(filename, content)
        result2 = await docling_service.process_document(filename)
        assert result2 is not None
        
        # Wait for processing
        await asyncio.sleep(2)
        count_after_v2 = await dependencies.qdrant_client.count_points()
        
        # Should handle versioning appropriately
        # (Implementation depends on versioning strategy)
        assert count_after_v2 >= count_after_v1
