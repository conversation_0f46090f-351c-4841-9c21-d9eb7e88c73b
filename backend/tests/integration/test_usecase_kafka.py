"""
Integration tests for the new usecase Kafka message processing.
Tests the complete flow: Kafka -> TTL conversion -> GraphDB -> Certificate processing -> Qdrant
"""
import pytest
import asyncio
import json
import logging
from pathlib import Path
from typing import Dict, Any

from core.dependencies import get_dependencies, cleanup_dependencies
from services.kafka_processor import KafkaProcessor
from services.ttl_converter import TTLConverter
from models.data_models import UseCaseMessage, GraphMetadataItem, Certificate
from tests.utils.cleanup import DataCleanup
from tests.utils.helpers import TestDataGenerator

logger = logging.getLogger(__name__)


@pytest.fixture
def sample_usecase_message():
    """Create a sample usecase message matching the new format."""
    return {
        "graphTemplate": "@prefix ex: <http://example.org/> .\\nNode1 hasRelation Node2 .",
        "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .\\n@prefix ex: <http://example.org/> .\\nex:TestBuilding rdf:type ex:Building .",
        "accessRights": None,
        "useCase": "PortfolioExample",
        "graphMetadata": [
            {
                "id": "018ac746-2503-4f2e-b681-8cb37d2f0e29",
                "classType": "https://ibpdi.datacat.org/class/Address",
                "propertiesValues": {
                    "StreetName": "Mainzer Landstraße",
                    "HouseNumber": "172-190",
                    "PostalCode": "60327",
                    "City": "Frankfurt am Main",
                    "Country": "Germany"
                }
            },
            {
                "id": "1892ebb9-9532-4dee-bddc-a7044da700a2",
                "classType": "https://ibpdi.datacat.org/class/Building",
                "propertiesValues": {
                    "PrimaryTypeOfBuilding": "Office",
                    "BuildingCode": "1000000005",
                    "Name": "Atrium Plaza",
                    "ValidFrom": "44926",
                    "PrimaryHeatingType": "District heating",
                    "ParkingSpaces": "151",
                    "EnergyEfficiencyClass": "E",
                    "ConstructionYear": "2003",
                    "Certificates": [
                        {
                            "pdf-path": "documents/PortfolioExample/05_Objekt_5/05_Objekt_5.pdf"
                        },
                        {
                            "pdf-path": "documents/PortfolioExample/23_Objekt_23/23_Objekt_23.pdf"
                        }
                    ]
                }
            }
        ]
    }


@pytest.mark.asyncio
async def test_usecase_message_parsing(sample_usecase_message):
    """Test parsing of the new usecase message format."""
    try:
        logger.info("Testing usecase message parsing...")
        
        # Parse the message
        usecase_message = UseCaseMessage(**sample_usecase_message)
        
        # Verify basic fields
        assert usecase_message.useCase == sample_usecase_message["useCase"]
        assert usecase_message.graphData is not None
        assert usecase_message.graphTemplate is not None
        assert len(usecase_message.graphMetadata) == 2
        
        # Verify metadata items
        address_item = usecase_message.graphMetadata[0]
        building_item = usecase_message.graphMetadata[1]
        
        assert "Address" in address_item.classType
        assert "Building" in building_item.classType
        assert address_item.propertiesValues["City"] == "Frankfurt am Main"
        assert building_item.propertiesValues["Name"] == "Atrium Plaza"
        
        # Verify certificates
        certificates = building_item.propertiesValues.get("Certificates", [])
        assert len(certificates) == 2
        
        # Test certificate parsing
        cert = Certificate(**certificates[0])
        assert cert.pdf_path == "documents/PortfolioExample/05_Objekt_5/05_Objekt_5.pdf"
        
        logger.info("✓ Usecase message parsing test passed")
        return True
        
    except Exception as e:
        logger.error(f"Usecase message parsing test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


@pytest.mark.asyncio
async def test_ttl_conversion_for_metadata():
    """Test TTL conversion for metadata items."""
    try:
        logger.info("Testing TTL conversion for metadata...")
        
        # Create a sample metadata item
        metadata_item = GraphMetadataItem(
            id="test-building-001",
            classType="https://ibpdi.datacat.org/class/Building",
            propertiesValues={
                "Name": "Test Building",
                "BuildingCode": "TB001",
                "EnergyEfficiencyClass": "A",
                "ConstructionYear": "2020"
            }
        )
        
        # Convert to TTL
        ttl_converter = TTLConverter()
        result = await ttl_converter.convert_metadata_to_ttl("TestUseCase", metadata_item)
        
        assert result.success
        assert result.ttl_content is not None
        assert result.triples_count > 0
        
        # Verify TTL content contains expected elements
        ttl_content = result.ttl_content
        assert "test-building-001" in ttl_content
        assert "Test Building" in ttl_content
        assert "TB001" in ttl_content
        
        logger.info(f"✓ TTL conversion test passed: {result.triples_count} triples generated")
        return True
        
    except Exception as e:
        logger.error(f"TTL conversion test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


@pytest.mark.asyncio
async def test_kafka_usecase_message_processing(sample_usecase_message):
    """Test the complete usecase message processing pipeline."""
    try:
        logger.info("Testing Kafka usecase message processing...")
        
        # Initialize dependencies
        deps = await get_dependencies()
        
        # Create Kafka processor
        processor = KafkaProcessor(deps)
        
        # Process the usecase message
        topic = "usecases.portfolio_example"
        await processor._handle_usecase_message(topic, sample_usecase_message)
        
        await cleanup_dependencies()
        
        logger.info("✓ Kafka usecase message processing test completed")
        return True
        
    except Exception as e:
        logger.error(f"Kafka usecase message processing test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


@pytest.mark.asyncio
async def test_certificate_extraction():
    """Test certificate extraction from building metadata."""
    try:
        logger.info("Testing certificate extraction...")
        
        # Create building metadata with certificates
        building_metadata = GraphMetadataItem(
            id="building-with-certs",
            classType="https://ibpdi.datacat.org/class/Building",
            propertiesValues={
                "Name": "Certified Building",
                "Certificates": [
                    {"pdf-path": "documents/test/cert1.pdf"},
                    {"pdf-path": "documents/test/cert2.pdf"}
                ]
            }
        )
        
        # Extract certificates
        certificates = building_metadata.propertiesValues.get("Certificates", [])
        assert len(certificates) == 2
        
        # Parse certificates
        cert_objects = [Certificate(**cert) for cert in certificates]
        assert cert_objects[0].pdf_path == "documents/test/cert1.pdf"
        assert cert_objects[1].pdf_path == "documents/test/cert2.pdf"
        
        logger.info("✓ Certificate extraction test passed")
        return True
        
    except Exception as e:
        logger.error(f"Certificate extraction test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


@pytest.mark.asyncio
async def test_all_usecase_kafka():
    """Run all usecase Kafka tests."""
    logger.info("Starting usecase Kafka tests...")

    sample_message = {
        "graphTemplate": "@prefix ex: <http://example.org/> .\\nNode1 hasRelation Node2 .",
        "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .\\n@prefix ex: <http://example.org/> .\\nex:TestBuilding rdf:type ex:Building .",
        "accessRights": None,
        "useCase": "TestCase",
        "graphMetadata": [
            {
                "id": "test-building-001",
                "classType": "https://ibpdi.datacat.org/class/Building",
                "propertiesValues": {
                    "Name": "Test Building",
                    "BuildingCode": "TB001"
                }
            }
        ]
    }

    results = []

    # Test message parsing
    results.append(await test_usecase_message_parsing(sample_message))

    # Test TTL conversion
    results.append(await test_ttl_conversion_for_metadata())

    # Test certificate extraction
    results.append(await test_certificate_extraction())

    # Test full processing pipeline
    results.append(await test_kafka_usecase_message_processing(sample_message))

    if all(results):
        logger.info("✓ All usecase Kafka tests passed!")
        return True
    else:
        logger.error("✗ Some usecase Kafka tests failed!")
        return False


if __name__ == "__main__":
    success = asyncio.run(test_all_usecase_kafka())
    exit(0 if success else 1)
