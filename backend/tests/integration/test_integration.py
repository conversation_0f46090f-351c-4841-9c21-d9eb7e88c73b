#!/usr/bin/env python3
"""
Comprehensive integration test for the RDF Agent system.
Tests the full pipeline from Kafka message to API response.
"""
import asyncio
import json
import logging
from pathlib import Path
import httpx
import pytest

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@pytest.mark.asyncio
async def test_full_pipeline():
    """Test the complete pipeline from Kafka to API."""
    try:
        logger.info("Starting full pipeline integration test...")
        
        # Step 1: Send test data to Kafka
        logger.info("Step 1: Sending test data to Kafka...")
        from infrastructure.kafka_client import KafkaClient
        from config.settings import Settings
        
        settings = Settings()
        kafka_client = KafkaClient(settings.kafka)
        await kafka_client.initialize()
        
        # Load example data
        example_file = Path("../.context/example_files/example_input.json")
        with open(example_file, 'r') as f:
            example_data = json.load(f)
        
        # Create a sample usecase message in the new format
        test_item = {
            "graphTemplate": "@prefix ex: <http://example.org/> .\\nNode1 hasRelation Node2 .",
            "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .\\n@prefix ex: <http://example.org/> .\\nex:TestBuilding rdf:type ex:Building .",
            "accessRights": None,
            "useCase": "IntegrationTest",
            "graphMetadata": [
                {
                    "id": "integration-test-building",
                    "classType": "https://ibpdi.datacat.org/class/Building",
                    "propertiesValues": {
                        "Name": "Integration Test Building",
                        "BuildingCode": "ITB001",
                        "EnergyEfficiencyClass": "B"
                    }
                }
            ]
        }

        topic = "usecases.integration_test"
        await kafka_client.produce_message(topic, test_item)
        logger.info("✓ Test data sent to Kafka")

        await kafka_client.close()

        # Step 2: Process the message
        logger.info("Step 2: Processing Kafka message...")
        from core.dependencies import get_dependencies, cleanup_dependencies
        from services.kafka_processor import KafkaProcessor

        deps = await get_dependencies()
        processor = KafkaProcessor(deps)

        # Process the message
        await processor._handle_usecase_message(topic, test_item)
        logger.info("✓ Kafka message processed")
        
        await cleanup_dependencies()
        
        # Step 3: Test TTL conversion
        logger.info("Step 3: Testing TTL conversion...")
        from services.ttl_converter import TTLConverter
        from models.data_models import GraphMetadataItem

        ttl_converter = TTLConverter()

        # Test metadata conversion
        metadata_item = GraphMetadataItem(**test_item["graphMetadata"][0])
        result = await ttl_converter.convert_metadata_to_ttl(test_item["useCase"], metadata_item)

        if result.success:
            logger.info(f"✓ TTL conversion successful: {result.triples_count} triples")
        else:
            logger.error(f"✗ TTL conversion failed: {result.error_message}")
            return False
        
        # Step 4: Test API endpoints
        logger.info("Step 4: Testing API endpoints...")
        base_url = "http://127.0.0.1:8001"
        
        async with httpx.AsyncClient() as client:
            # Test health check
            response = await client.get(f"{base_url}/health")
            if response.status_code == 200:
                logger.info("✓ API health check passed")
            else:
                logger.error(f"✗ API health check failed: {response.status_code}")
                return False
            
            # Test session creation
            session_data = {"metadata": {"test": True, "pipeline_test": True}}
            response = await client.post(f"{base_url}/api/v1/sessions/", json=session_data)
            if response.status_code == 200:
                session_id = response.json()["session_id"]
                logger.info(f"✓ Session created: {session_id}")
            else:
                logger.error(f"✗ Session creation failed: {response.status_code}")
                return False
            
            # Test query endpoint (this will fail due to API key, but we test the endpoint)
            query_data = {
                "query_text": "Find buildings with energy certificates",
                "query_type": "natural_language",
                "session_id": session_id
            }
            response = await client.post(f"{base_url}/api/v1/query/", json=query_data)
            logger.info(f"✓ Query endpoint tested (status: {response.status_code})")
        
        # Step 5: Test document stats
        logger.info("Step 5: Testing document statistics...")
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{base_url}/api/v1/documents/collection/stats")
            if response.status_code == 200:
                stats = response.json()
                logger.info(f"✓ Document stats: {stats}")
            else:
                logger.error(f"✗ Document stats failed: {response.status_code}")
                return False
        
        logger.info("✓ Full pipeline integration test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"Full pipeline integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


@pytest.mark.asyncio
async def test_infrastructure_health():
    """Test all infrastructure components are healthy."""
    try:
        logger.info("Testing infrastructure health...")
        
        from core.dependencies import get_dependencies, cleanup_dependencies
        
        deps = await get_dependencies()
        
        # Test each component
        components = {
            "Database": deps.database_manager,
            "Kafka": deps.kafka_client,
            "GraphDB": deps.graphdb_client,
            "MinIO": deps.minio_client,
            "Qdrant": deps.qdrant_client
        }
        
        healthy_components = []
        for name, component in components.items():
            if component:
                healthy_components.append(name)
                logger.info(f"✓ {name} is initialized")
            else:
                logger.error(f"✗ {name} is not initialized")
        
        await cleanup_dependencies()
        
        if len(healthy_components) == len(components):
            logger.info("✓ All infrastructure components are healthy!")
            return True
        else:
            logger.error(f"✗ Only {len(healthy_components)}/{len(components)} components are healthy")
            return False
        
    except Exception as e:
        logger.error(f"Infrastructure health test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


@pytest.mark.asyncio
async def test_data_flow():
    """Test data flow through the system."""
    try:
        logger.info("Testing data flow...")
        
        from core.dependencies import get_dependencies, cleanup_dependencies
        from services.ttl_converter import TTLConverter
        from agents.rag_agent import RAGAgent
        import uuid
        
        deps = await get_dependencies()
        
        # Step 1: Convert sample data to TTL
        sample_metadata = GraphMetadataItem(
            id="test-data-flow-building",
            classType="https://ibpdi.datacat.org/class/Building",
            propertiesValues={
                "Name": "Data Flow Test Building",
                "BuildingCode": "DFTB001"
            }
        )

        ttl_converter = TTLConverter()
        ttl_result = await ttl_converter.convert_metadata_to_ttl("TestCase", sample_metadata)
        
        if not ttl_result.success:
            logger.error(f"✗ TTL conversion failed: {ttl_result.error_message}")
            return False
        
        logger.info(f"✓ TTL conversion: {ttl_result.triples_count} triples")
        
        # Step 2: Upload to GraphDB
        success = await deps.graphdb_client.upload_ttl_data(ttl_result.ttl_content)
        if success:
            logger.info("✓ Data uploaded to GraphDB")
        else:
            logger.error("✗ Failed to upload data to GraphDB")
            return False
        
        # Step 3: Add document to vector database
        rag_agent = RAGAgent(deps)
        doc_id = str(uuid.uuid4())
        doc_content = "This is a test building certificate with energy efficiency rating A+."
        
        success = await rag_agent.add_document(doc_id, doc_content, {"type": "certificate"})
        if success:
            logger.info("✓ Document added to vector database")
        else:
            logger.error("✗ Failed to add document to vector database")
            return False
        
        # Step 4: Test SPARQL query
        sparql_query = """
        PREFIX ex: <http://example.org/>
        SELECT ?s ?p ?o WHERE {
            ?s ?p ?o .
        } LIMIT 5
        """
        
        results = await deps.graphdb_client.execute_sparql_query(sparql_query)
        if results:
            logger.info(f"✓ SPARQL query returned {len(results)} results")
        else:
            logger.error("✗ SPARQL query returned no results")
            return False
        
        # Clean up
        await rag_agent.delete_document(doc_id)
        await cleanup_dependencies()
        
        logger.info("✓ Data flow test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"Data flow test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


@pytest.mark.asyncio
async def test_all_integration():
    """Run all integration tests."""
    logger.info("Starting comprehensive integration tests...")

    # Test infrastructure health
    await test_infrastructure_health()

    # Test data flow
    await test_data_flow()

    # Test full pipeline
    await test_full_pipeline()

    logger.info("🎉 ALL INTEGRATION TESTS PASSED! 🎉")
    logger.info("The RDF Agent system is working correctly!")


async def main():
    """Run all integration tests for standalone execution."""
    logger.info("Starting comprehensive integration tests...")

    results = []

    # Test infrastructure health
    results.append(await test_infrastructure_health())

    # Test data flow
    results.append(await test_data_flow())

    # Test full pipeline
    results.append(await test_full_pipeline())

    if all(results):
        logger.info("🎉 ALL INTEGRATION TESTS PASSED! 🎉")
        logger.info("The RDF Agent system is working correctly!")
        return True
    else:
        failed_count = len([r for r in results if not r])
        logger.error(f"❌ {failed_count}/{len(results)} integration tests failed!")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
