"""
Integration tests for the new GraphDB client implementation.
Tests against a real GraphDB instance.
"""
import pytest
import async<PERSON>
from typing import Dict, Any

from config.settings import GraphDBSettings
from infrastructure.graphdb_client_new import GraphDBClient


@pytest.mark.integration
@pytest.mark.requires_services
class TestGraphDBClientIntegration:
    """Integration tests for the new GraphDB client."""
    
    @pytest.fixture
    async def graphdb_settings(self):
        """Create GraphDB settings for testing."""
        return GraphDBSettings(
            url="http://localhost:7200",
            default_repository="PortfolioExample",
            store_type="graphdb"
        )
    
    @pytest.fixture
    async def client(self, graphdb_settings):
        """Create and initialize a GraphDB client."""
        client = GraphDBClient(graphdb_settings)
        await client.initialize()
        yield client
        await client.close()
    
    async def test_client_initialization(self, graphdb_settings):
        """Test client initialization and cleanup."""
        client = GraphDBClient(graphdb_settings)
        
        # Test initialization
        await client.initialize()
        assert client._store is not None
        assert client.base_url == "http://localhost:7200"
        assert client.repository == "PortfolioExample"
        
        # Test cleanup
        await client.close()
    
    async def test_health_check(self, client):
        """Test health check functionality."""
        result = await client.health_check()
        assert result is True
    
    async def test_repository_operations(self, client):
        """Test repository-related operations."""
        # Test getting repositories
        repos = await client.get_repositories()
        assert isinstance(repos, list)
        
        # Test getting repository size
        size = await client.get_repository_size()
        assert isinstance(size, int)
        assert size >= 0
    
    async def test_basic_sparql_queries(self, client):
        """Test basic SPARQL query operations."""
        # Test ASK query
        ask_query = "ASK { ?s ?p ?o }"
        result = await client.execute_sparql_query(ask_query)
        
        assert isinstance(result, dict)
        assert "boolean" in result or "head" in result
        
        # Test SELECT query
        select_query = "SELECT ?s ?p ?o WHERE { ?s ?p ?o } LIMIT 5"
        result = await client.execute_sparql_query(select_query)
        
        assert isinstance(result, dict)
        assert "head" in result
        assert "results" in result
        assert "vars" in result["head"]
        assert "bindings" in result["results"]
    
    async def test_sparql_update_operations(self, client):
        """Test SPARQL update operations."""
        # Test data insertion
        test_triple = """
        INSERT DATA {
            <http://example.org/test/subject> <http://example.org/test/predicate> "test_value" .
        }
        """
        
        result = await client.execute_sparql_update(test_triple)
        assert result is True
        
        # Verify the data was inserted
        verify_query = """
        SELECT ?o WHERE {
            <http://example.org/test/subject> <http://example.org/test/predicate> ?o
        }
        """
        
        query_result = await client.execute_sparql_query(verify_query)
        bindings = query_result["results"]["bindings"]
        assert len(bindings) > 0
        assert bindings[0]["o"]["value"] == "test_value"
        
        # Clean up - delete the test data
        delete_query = """
        DELETE DATA {
            <http://example.org/test/subject> <http://example.org/test/predicate> "test_value" .
        }
        """
        
        result = await client.execute_sparql_update(delete_query)
        assert result is True
    
    async def test_ttl_data_upload(self, client):
        """Test uploading TTL data."""
        ttl_content = """
        @prefix ex: <http://example.org/test/> .
        
        ex:person1 ex:name "Alice" ;
                   ex:age "25" .
        
        ex:person2 ex:name "Bob" ;
                   ex:age "30" .
        """
        
        # Upload TTL data
        result = await client.upload_ttl_data(ttl_content)
        assert result is True
        
        # Verify the data was uploaded
        verify_query = """
        PREFIX ex: <http://example.org/test/>
        SELECT ?person ?name ?age WHERE {
            ?person ex:name ?name ;
                    ex:age ?age .
        }
        ORDER BY ?name
        """
        
        query_result = await client.execute_sparql_query(verify_query)
        bindings = query_result["results"]["bindings"]
        assert len(bindings) == 2
        
        # Check Alice's data
        alice = next(b for b in bindings if b["name"]["value"] == "Alice")
        assert alice["age"]["value"] == "25"
        
        # Check Bob's data
        bob = next(b for b in bindings if b["name"]["value"] == "Bob")
        assert bob["age"]["value"] == "30"
        
        # Clean up - delete the test data
        cleanup_query = """
        PREFIX ex: <http://example.org/test/>
        DELETE WHERE {
            ?person ex:name ?name ;
                    ex:age ?age .
        }
        """
        
        result = await client.execute_sparql_update(cleanup_query)
        assert result is True
    
    async def test_error_handling(self, client):
        """Test error handling for invalid queries."""
        # Test invalid SPARQL query
        invalid_query = "INVALID SPARQL QUERY"
        
        with pytest.raises(Exception):
            await client.execute_sparql_query(invalid_query)
        
        # Test invalid SPARQL update
        invalid_update = "INVALID UPDATE QUERY"
        result = await client.execute_sparql_update(invalid_update)
        assert result is False
    
    async def test_backward_compatibility(self, client):
        """Test backward compatibility with the original API."""
        # Test that the client has all the expected methods
        assert hasattr(client, 'initialize')
        assert hasattr(client, 'close')
        assert hasattr(client, 'health_check')
        assert hasattr(client, 'execute_sparql_query')
        assert hasattr(client, 'execute_sparql_update')
        assert hasattr(client, 'upload_ttl_data')
        assert hasattr(client, 'get_repositories')
        assert hasattr(client, 'clear_repository')
        assert hasattr(client, 'get_repository_size')
        
        # Test that properties are accessible
        assert client.base_url == "http://localhost:7200"
        assert client.repository == "PortfolioExample"
        assert client.session is not None
    
    async def test_different_repositories(self, client):
        """Test operations with different repositories."""
        # Test query with specific repository
        query = "SELECT ?s ?p ?o WHERE { ?s ?p ?o } LIMIT 1"
        
        # Query default repository
        result1 = await client.execute_sparql_query(query)
        assert isinstance(result1, dict)
        
        # Query specific repository (should work the same for GraphDB)
        result2 = await client.execute_sparql_query(query, repository="PortfolioExample")
        assert isinstance(result2, dict)
        
        # Results should have the same structure
        assert result1.keys() == result2.keys()


@pytest.mark.integration
@pytest.mark.requires_services
class TestGraphDBClientPerformance:
    """Performance tests for the GraphDB client."""
    
    @pytest.fixture
    async def client(self):
        """Create and initialize a GraphDB client."""
        settings = GraphDBSettings(
            url="http://localhost:7200",
            default_repository="PortfolioExample"
        )
        client = GraphDBClient(settings)
        await client.initialize()
        yield client
        await client.close()
    
    async def test_concurrent_queries(self, client):
        """Test concurrent query execution."""
        query = "SELECT ?s ?p ?o WHERE { ?s ?p ?o } LIMIT 10"
        
        # Execute multiple queries concurrently
        tasks = [client.execute_sparql_query(query) for _ in range(5)]
        results = await asyncio.gather(*tasks)
        
        # All queries should succeed
        assert len(results) == 5
        for result in results:
            assert isinstance(result, dict)
            assert "head" in result
            assert "results" in result
    
    async def test_large_result_handling(self, client):
        """Test handling of large query results."""
        # Query for a larger result set
        query = "SELECT ?s ?p ?o WHERE { ?s ?p ?o } LIMIT 1000"
        
        result = await client.execute_sparql_query(query)
        
        assert isinstance(result, dict)
        assert "results" in result
        bindings = result["results"]["bindings"]
        assert len(bindings) <= 1000  # Should not exceed the limit
