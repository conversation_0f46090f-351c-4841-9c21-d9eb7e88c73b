"""
Comprehensive full-system integration tests for the RDF Agent System.
Tests the complete workflow from Kafka message consumption to AI agent responses.
"""
import asyncio
import json
import logging
import time
from pathlib import Path
from typing import Dict, List, Any
import pytest
import pytest_asyncio

from core.dependencies import get_dependencies, cleanup_dependencies
from services.kafka_processor import KafkaProcessor
from agents.orchestrator_agent import OrchestratorAgent
from models.data_models import QueryRequest, QueryResponse
from tests.utils.cleanup import DataCleanup
from infrastructure.kafka_client import KafkaClient

logger = logging.getLogger(__name__)


@pytest.mark.integration
@pytest.mark.requires_services
class TestFullSystemIntegration:
    """Comprehensive integration tests for the complete system."""
    
    @pytest_asyncio.fixture(scope="class")
    async def real_dependencies(self):
        """Get real dependencies for integration testing."""
        deps = await get_dependencies()
        yield deps
        await cleanup_dependencies()

    @pytest_asyncio.fixture(scope="class")
    async def cleanup_util(self, real_dependencies):
        """Create cleanup utility for test data."""
        return DataCleanup(
            real_dependencies.minio_client,
            real_dependencies.qdrant_client,
            real_dependencies.graphdb_client
        )

    @pytest_asyncio.fixture(autouse=True)
    async def setup_and_cleanup(self, cleanup_util):
        """Setup and cleanup test data before and after each test."""
        # Cleanup before test
        await cleanup_util.cleanup_all()
        yield
        # Cleanup after test
        await cleanup_util.cleanup_all()
    
    @pytest.fixture
    def sample_kafka_data(self) -> List[Dict[str, Any]]:
        """Load sample data from the example file."""
        example_file = Path(__file__).parent.parent.parent / ".context" / "example_files" / "example_input.json"
        
        if not example_file.exists():
            # Fallback sample data if file doesn't exist
            return [
                {
                    "graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .",
                    "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\n@prefix inst: <https://example.com/>.\n\ninst:test-address-1 ibpdi:hasBuilding inst:test-building-1.\n",
                    "accessRights": None,
                    "useCase": "TestPortfolio",
                    "graphMetadata": [
                        {
                            "id": "test-address-1",
                            "classType": "https://ibpdi.datacat.org/class/Address",
                            "propertiesValues": {
                                "StreetName": "Test Street",
                                "HouseNumber": "123",
                                "PostalCode": "12345",
                                "City": "Test City",
                                "Country": "Test Country"
                            }
                        },
                        {
                            "id": "test-building-1",
                            "classType": "https://ibpdi.datacat.org/class/Building",
                            "propertiesValues": {
                                "PrimaryTypeOfBuilding": "Office",
                                "BuildingCode": "TEST001",
                                "Name": "Test Building",
                                "ValidFrom": "44926",
                                "PrimaryHeatingType": "District heating",
                                "ParkingSpaces": "50",
                                "EnergyEfficiencyClass": "A+",
                                "ConstructionYear": "2020"
                            }
                        }
                    ]
                }
            ]
        
        with open(example_file, 'r') as f:
            data = json.load(f)
        
        # Return first 3 items for testing to avoid long wait times
        return data[:3]
    
    @pytest.mark.asyncio
    async def test_kafka_message_processing_flow(
        self,
        real_dependencies,
        sample_kafka_data: List[Dict[str, Any]]
    ):
        """Test complete Kafka message processing flow."""
        logger.info("Starting Kafka message processing flow test")
        
        # Create Kafka processor
        processor = KafkaProcessor(real_dependencies)
        
        # Process sample data items
        processed_count = 0
        for i, item in enumerate(sample_kafka_data):
            logger.info(f"Processing item {i+1}/{len(sample_kafka_data)}")
            
            try:
                # Extract building and address data from metadata
                for metadata_item in item.get("graphMetadata", []):
                    class_type = metadata_item.get("classType", "")
                    
                    if "Building" in class_type:
                        # Process as building data
                        building_data = {
                            "building_id": metadata_item["id"],
                            "usecase_name": item.get("useCase", "TestCase"),
                            "properties": metadata_item.get("propertiesValues", {}),
                            "graph_data": item.get("graphData", ""),
                            "metadata": metadata_item
                        }
                        
                        await processor._handle_building_message("building_data", building_data)
                        processed_count += 1
                        
                    elif "Address" in class_type:
                        # Process as address data
                        address_data = {
                            "address_id": metadata_item["id"],
                            "usecase_name": item.get("useCase", "TestCase"),
                            "properties": metadata_item.get("propertiesValues", {}),
                            "graph_data": item.get("graphData", ""),
                            "metadata": metadata_item
                        }
                        
                        await processor._handle_address_message("address_data", address_data)
                        processed_count += 1
                
                # Small delay between items
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.error(f"Error processing item {i}: {e}")
                continue
        
        logger.info(f"Processed {processed_count} data items")
        assert processed_count > 0, "No data items were processed successfully"
        
        # Wait for processing to complete
        await asyncio.sleep(5)
        
        # Verify data was stored in GraphDB
        sparql_query = """
        SELECT ?s ?p ?o WHERE {
            ?s ?p ?o .
        } LIMIT 10
        """
        
        try:
            result = await real_dependencies.graphdb_client.execute_sparql_query(sparql_query)
            assert result is not None, "GraphDB query failed"
            
            bindings = result.get("results", {}).get("bindings", [])
            logger.info(f"Found {len(bindings)} triples in GraphDB")
            assert len(bindings) > 0, "No RDF data found in GraphDB"
            
        except Exception as e:
            logger.warning(f"GraphDB verification failed: {e}")
            # Continue test even if GraphDB is not available
    
    @pytest.mark.asyncio
    async def test_document_processing_and_embedding_flow(
        self,
        real_dependencies,
        tmp_path: Path
    ):
        """Test complete document processing and embedding flow."""
        logger.info("Starting document processing and embedding flow test")
        
        # Create test documents
        test_documents = []
        
        # Energy efficiency document
        energy_doc = tmp_path / "energy_efficiency.txt"
        energy_content = """
        Energy Efficiency in Buildings
        
        LED lighting systems can reduce energy consumption by up to 75% compared to traditional bulbs.
        Smart HVAC systems with variable speed drives improve efficiency by 30-40%.
        Proper insulation reduces heat loss and improves building performance.
        Solar panels can offset 50-100% of building energy consumption.
        Buildings with A+ energy rating consume 80% less energy than standard buildings.
        """
        energy_doc.write_text(energy_content)
        test_documents.append(energy_doc)
        
        # Sustainability document
        sustainability_doc = tmp_path / "sustainability.txt"
        sustainability_content = """
        Sustainable Building Practices
        
        Green building materials like recycled steel reduce carbon footprint by 60%.
        Rainwater harvesting systems reduce water consumption by 30-50%.
        Construction waste recycling diverts 75% of materials from landfills.
        Green roofs reduce building energy consumption by 15-20%.
        LEED and BREEAM certifications ensure environmental performance.
        """
        sustainability_doc.write_text(sustainability_content)
        test_documents.append(sustainability_doc)
        
        # Process documents through the system
        processor = KafkaProcessor(real_dependencies)
        
        for doc_path in test_documents:
            # Upload document to MinIO
            with open(doc_path, 'rb') as f:
                content = f.read()
            
            object_path = f"documents/test/{doc_path.name}"
            
            try:
                await real_dependencies.minio_client.upload_data(object_path, content)
                logger.info(f"Uploaded document: {object_path}")
                
                # Process document message
                document_message = {
                    "usecase_name": "TestCase",
                    "folders": ["test"],
                    "filename": doc_path.name,
                    "object_path": object_path
                }
                
                await processor._handle_document_message("document_processing", document_message)
                logger.info(f"Processed document: {doc_path.name}")
                
            except Exception as e:
                logger.error(f"Error processing document {doc_path.name}: {e}")
                continue
        
        # Wait for processing to complete
        await asyncio.sleep(10)
        
        # Verify embeddings were created in Qdrant
        try:
            point_count = await real_dependencies.qdrant_client.count_points()
            logger.info(f"Found {point_count} points in Qdrant")
            assert point_count > 0, "No embeddings found in Qdrant"
            
        except Exception as e:
            logger.warning(f"Qdrant verification failed: {e}")
            # Continue test even if Qdrant is not available
    
    @pytest.mark.asyncio
    async def test_end_to_end_query_flow(
        self,
        real_dependencies,
        sample_kafka_data: List[Dict[str, Any]]
    ):
        """Test complete end-to-end query flow."""
        logger.info("Starting end-to-end query flow test")
        
        # First, process some data
        processor = KafkaProcessor(real_dependencies)
        
        # Process a subset of sample data
        for item in sample_kafka_data[:2]:  # Process only first 2 items
            for metadata_item in item.get("graphMetadata", []):
                class_type = metadata_item.get("classType", "")
                
                if "Building" in class_type:
                    building_data = {
                        "building_id": metadata_item["id"],
                        "usecase_name": item.get("useCase", "TestCase"),
                        "properties": metadata_item.get("propertiesValues", {}),
                        "graph_data": item.get("graphData", ""),
                        "metadata": metadata_item
                    }
                    await processor._handle_building_message("building_data", building_data)
        
        # Wait for processing
        await asyncio.sleep(5)
        
        # Create orchestrator agent
        orchestrator = OrchestratorAgent(real_dependencies)
        
        # Test various query types
        test_queries = [
            {
                "query": "What buildings do we have in the database?",
                "expected_keywords": ["building"]
            },
            {
                "query": "Find buildings with good energy efficiency",
                "expected_keywords": ["energy", "efficiency"]
            },
            {
                "query": "What are the addresses of the buildings?",
                "expected_keywords": ["address"]
            }
        ]
        
        successful_queries = 0
        for i, test_query in enumerate(test_queries):
            try:
                import uuid
                query_request = QueryRequest(
                    query_id=str(uuid.uuid4()),
                    query_text=test_query["query"],
                    query_type="natural_language",
                    session_id=f"test-session-{i}"
                )

                logger.info(f"Executing query: {test_query['query']}")
                response = await orchestrator.process_query(query_request)

                assert isinstance(response, QueryResponse), "Invalid response type"
                assert response.response_text is not None, "Empty response"

                logger.info(f"Query response: {response.response_text[:200]}...")
                successful_queries += 1
                
            except Exception as e:
                logger.error(f"Query failed: {e}")
                continue
        
        logger.info(f"Successfully executed {successful_queries}/{len(test_queries)} queries")
        assert successful_queries > 0, "No queries executed successfully"
