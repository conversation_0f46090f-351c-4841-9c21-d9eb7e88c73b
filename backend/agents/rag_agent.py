"""
RAG (Retrieval-Augmented Generation) Agent for document search and retrieval.
Handles semantic search over processed documents using vector embeddings.
"""
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

from pydantic import BaseModel
from pydantic_ai import Agent, RunContext
from pydantic_ai.models.openai import OpenAIChatModel
from pydantic_ai.providers.openrouter import OpenRouterProvider
from sentence_transformers import SentenceTransformer
from qdrant_client.http.models import PointStruct

from core.dependencies import Dependencies


logger = logging.getLogger(__name__)


class RAGContext(BaseModel):
    """Context for the RAG agent."""
    session_id: Optional[str] = None
    query_text: str
    search_limit: int = 5


class RAGAgent:
    """Agent specialized in document retrieval and semantic search."""
    
    def __init__(self, dependencies: Dependencies):
        self.deps = dependencies
        self.embedding_model = None
        
        # Create PydanticAI agent with OpenRouter provider
        provider = OpenRouterProvider(api_key=dependencies.settings.ai.or_api_key)
        model = OpenAIChatModel('openai/gpt-4.1-mini', provider=provider)
        self.agent = Agent(
            model,
            system_prompt=self._get_system_prompt()
        )
        
        # Register tools
        self._register_tools()

    def _get_system_prompt(self) -> str:
        """Get the system prompt for the RAG agent."""
        return """
        You are a specialized RAG (Retrieval-Augmented Generation) Agent for document search and analysis.
        
        Your capabilities include:
        1. Semantic search over processed documents
        2. Document content analysis and summarization
        3. Extracting relevant information from documents
        4. Providing context-aware responses based on document content
        
        The document collection contains:
        - Building and construction documents
        - Address and location information
        - Technical specifications and reports
        - Various document formats processed through Docling
        
        When searching documents:
        1. Use semantic similarity to find relevant content
        2. Consider context and user intent
        3. Provide clear citations and source references
        4. Summarize findings in a helpful way
        5. Highlight key information and insights
        
        Always provide accurate information with proper source attribution.
        If you cannot find relevant information, clearly state this limitation.
        """

    def _register_tools(self):
        """Register tools for the RAG agent."""
        
        @self.agent.tool
        async def search_vector_database(ctx: RunContext[RAGContext], 
                                       query_text: str, 
                                       limit: int = 5,
                                       score_threshold: float = 0.7) -> Dict[str, Any]:
            """Search the vector database for relevant documents."""
            try:
                # Get query embedding
                query_embedding = await self._get_embedding(query_text)
                
                # Search Qdrant
                results = await self.deps.qdrant_client.search(
                    query_vector=query_embedding,
                    limit=limit,
                    score_threshold=score_threshold
                )
                
                return {
                    "success": True,
                    "results": results,
                    "query": query_text,
                    "count": len(results)
                }
                
            except Exception as e:
                logger.error(f"Error searching vector database: {e}")
                return {
                    "success": False,
                    "error": str(e),
                    "query": query_text
                }

        @self.agent.tool
        async def get_document_content(ctx: RunContext[RAGContext], 
                                     document_id: str) -> Dict[str, Any]:
            """Retrieve full content of a specific document."""
            try:
                # Get document from Qdrant
                point = await self.deps.qdrant_client.get_point(document_id)
                
                if not point:
                    return {
                        "success": False,
                        "error": f"Document {document_id} not found"
                    }
                
                return {
                    "success": True,
                    "document_id": document_id,
                    "content": point.get("payload", {}),
                    "metadata": point.get("payload", {}).get("metadata", {})
                }
                
            except Exception as e:
                logger.error(f"Error getting document content: {e}")
                return {
                    "success": False,
                    "error": str(e),
                    "document_id": document_id
                }

        @self.agent.tool
        async def get_collection_stats(ctx: RunContext[RAGContext]) -> Dict[str, Any]:
            """Get statistics about the document collection."""
            try:
                count = await self.deps.qdrant_client.count_points()
                
                return {
                    "success": True,
                    "total_documents": count,
                    "collection_name": self.deps.qdrant_client.collection_name
                }
                
            except Exception as e:
                logger.error(f"Error getting collection stats: {e}")
                return {
                    "success": False,
                    "error": str(e)
                }

    async def _get_embedding_model(self):
        """Get or initialize the embedding model."""
        if self.embedding_model is None:
            try:
                # Use a lightweight sentence transformer model
                self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
                logger.info("Initialized embedding model: all-MiniLM-L6-v2")
            except Exception as e:
                logger.error(f"Error initializing embedding model: {e}")
                raise
        return self.embedding_model

    async def _get_embedding(self, text: str) -> List[float]:
        """Get embedding for a text string."""
        try:
            model = await self._get_embedding_model()
            embedding = model.encode(text, convert_to_tensor=False)
            return embedding.tolist()
        except Exception as e:
            logger.error(f"Error getting embedding: {e}")
            raise

    async def search_documents(self, query: str, limit: int = 5, 
                             session_id: Optional[str] = None) -> Dict[str, Any]:
        """Search documents and return relevant results."""
        try:
            # Create context
            context = RAGContext(
                dependencies=self.deps,
                session_id=session_id,
                query_text=query,
                search_limit=limit
            )
            
            # Use the agent to search and analyze
            result = await self.agent.run(
                f"Search for documents relevant to: {query}",
                deps=context
            )
            
            return {
                "response": result.output,
                "query": query,
                "limit": limit
            }
            
        except Exception as e:
            logger.error(f"Error searching documents: {e}")
            return {
                "error": str(e),
                "query": query
            }

    async def add_document(self, document_id: str, content: str, 
                          metadata: Optional[Dict[str, Any]] = None) -> bool:
        """Add a document to the vector database."""
        try:
            # Get embedding for the content
            embedding = await self._get_embedding(content)
            
            # Create point for Qdrant
            point = PointStruct(
                id=document_id,
                vector=embedding,
                payload={
                    "content": content,
                    "metadata": metadata or {},
                    "indexed_at": datetime.utcnow().isoformat()
                }
            )
            
            # Add to Qdrant
            success = await self.deps.qdrant_client.upsert_points([point])
            
            if success:
                logger.info(f"Added document to vector database: {document_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error adding document to vector database: {e}")
            return False

    async def add_document_batch(self, documents: List[Dict[str, Any]]) -> int:
        """Add multiple documents to the vector database."""
        try:
            points = []
            
            for doc in documents:
                document_id = doc.get("id")
                content = doc.get("content", "")
                metadata = doc.get("metadata", {})
                
                if not document_id or not content:
                    logger.warning(f"Skipping document with missing id or content: {doc}")
                    continue
                
                # Get embedding
                embedding = await self._get_embedding(content)
                
                # Create point
                point = PointStruct(
                    id=document_id,
                    vector=embedding,
                    payload={
                        "content": content,
                        "metadata": metadata,
                        "indexed_at": datetime.utcnow().isoformat()
                    }
                )
                
                points.append(point)
            
            # Add batch to Qdrant
            if points:
                success = await self.deps.qdrant_client.upsert_points(points)
                if success:
                    logger.info(f"Added {len(points)} documents to vector database")
                    return len(points)
            
            return 0
            
        except Exception as e:
            logger.error(f"Error adding document batch: {e}")
            return 0

    async def delete_document(self, document_id: str) -> bool:
        """Delete a document from the vector database."""
        try:
            success = await self.deps.qdrant_client.delete_points([document_id])
            
            if success:
                logger.info(f"Deleted document from vector database: {document_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error deleting document: {e}")
            return False
