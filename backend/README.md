# RDF Agent Backend

An agentic AI system for RDF database querying and document processing using PydanticAI, FastAPI, and various data processing services.

## Architecture

This system implements the architecture defined in `ARCHITECTURE.md` with the following key components:

- **Orchestrator Agent**: Main coordination agent using PydanticAI
- **RDF Query Agent**: Specialized agent for SPARQL queries and RDF operations
- **RAG Agent**: Document retrieval and semantic search agent
- **Kafka Processor**: Message processing service for building/address data
- **FastAPI Server**: REST API for external access
- **Multiple Infrastructure Clients**: GraphDB, Qdrant, MinIO, Kafka

## Prerequisites

Ensure the following services are running and accessible:

- **GraphDB**: RDF database for storing graph data
- **Qdrant**: Vector database for semantic search
- **MinIO**: Object storage for documents
- **Kafka**: Message broker for data processing
- **SQLite**: Local database for session storage (auto-created)

## Installation

1. Install dependencies:

```bash
uv sync
```

2. Configure environment variables in `.env` (already exists):

```bash
# The .env file should contain:
# - GraphDB connection details
# - Qdrant connection details
# - Min<PERSON> credentials
# - Kafka configuration
# - API keys for AI services
```

## Usage

### 1. Test the Implementation

First, test that everything is working:

```bash
# Test basic functionality
uv run python test_implementation.py

# Test service connections
uv run python main.py test-connections

# Initialize database
uv run python main.py init-db
```

### 2. Process Sample Data

Process the example data from the context files:

```bash
uv run python main.py process-sample-data
```

### 3. Run the API Server

Start the FastAPI server:

```bash
# Development mode
uv run python main.py api --reload

# Production mode
uv run python main.py api --host 0.0.0.0 --port 8000
```

The API will be available at:

- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health
- **Root Info**: http://localhost:8000/

### 4. Run the Kafka Processor

Start the Kafka message processor:

```bash
uv run python main.py kafka-processor
```

### 5. Test Queries

Test queries via CLI:

```bash
# Natural language query
uv run python main.py query "Find all buildings in Berlin"

# SPARQL query
uv run python main.py query "SELECT ?building WHERE { ?building a schema:Building }" --query-type sparql
```

## API Endpoints

### Query Endpoints (`/api/v1/query`)

- `POST /` - Process natural language or SPARQL queries
- `POST /sparql` - Execute SPARQL queries directly
- `POST /convert-to-sparql` - Convert natural language to SPARQL
- `GET /repository/info` - Get RDF repository information
- `GET /examples` - Get example queries

### Document Endpoints (`/api/v1/documents`)

- `POST /search` - Search documents semantically
- `POST /upload` - Upload and process documents
- `POST /add` - Add documents directly to vector DB
- `GET /{document_id}` - Get specific document
- `DELETE /{document_id}` - Delete document
- `GET /collection/stats` - Get collection statistics

### Session Endpoints (`/api/v1/sessions`)

- `POST /` - Create new session
- `GET /{session_id}` - Get session details
- `GET /{session_id}/history` - Get conversation history
- `GET /{session_id}/context` - Get recent context
- `GET /{session_id}/summary` - Get session summary
- `PATCH /{session_id}/metadata` - Update session metadata

### Admin Endpoints (`/api/v1/admin`)

- `GET /status` - System status and health
- `GET /repositories` - List RDF repositories
- `POST /repositories/{repo}/upload-ttl` - Upload TTL data
- `DELETE /repositories/{repo}/clear` - Clear repository
- `GET /vector-db/stats` - Vector database statistics
- `GET /object-storage/stats` - Object storage statistics

## Data Flow

1. **Kafka Messages**: Building/address data arrives via Kafka topics
2. **TTL Conversion**: JSON data is converted to RDF/TTL format
3. **Storage**: TTL data is stored in GraphDB, searchable content in Qdrant
4. **Document Processing**: Files are processed with Docling and stored in MinIO
5. **Query Processing**: Natural language queries are processed by agents
6. **Response Generation**: Comprehensive responses using RDF data and documents

## Configuration

Key configuration files:

- `config/settings.py` - Main settings with environment variable support
- `config/config.yaml` - Default configuration values
- `.env` - Environment-specific settings (already exists)

## Development

### Project Structure

```
backend/
├── agents/              # PydanticAI agents
├── api/                 # FastAPI application and routes
├── config/              # Configuration management
├── core/                # Dependency injection and core utilities
├── infrastructure/      # External service clients
├── models/              # Pydantic data models
├── services/            # Business logic services
├── utils/               # Utility functions
├── tests/               # Test files
├── main.py              # CLI application entry point
└── test_implementation.py # Implementation test script
```

### Key Components

- **Dependencies**: Centralized dependency injection in `core/dependencies.py`
- **Agents**: PydanticAI-based agents with tool integration
- **Infrastructure**: Async clients for external services
- **Services**: Business logic for TTL conversion, document processing, etc.
- **API**: FastAPI routes with proper error handling and validation

### Testing

Run the test script to verify functionality:

```bash
uv run python test_implementation.py
```

This tests:

- Basic system initialization
- TTL conversion functionality
- Sample data processing
- API model validation

## Troubleshooting

### Common Issues

1. **Service Connection Errors**: Ensure all external services are running
2. **Import Errors**: Run `uv sync` to install dependencies
3. **Database Errors**: Run `uv run python main.py init-db`
4. **Permission Errors**: Check file permissions and MinIO credentials

### Debugging

Enable debug logging by setting the log level:

```bash
export LOG_LEVEL=DEBUG
```

### Health Checks

Check system health:

```bash
# Via CLI
uv run python main.py test-connections

# Via API
curl http://localhost:8000/health
```

## Production Deployment

For production deployment:

1. Set appropriate environment variables
2. Use a production WSGI server (uvicorn with multiple workers)
3. Set up proper logging and monitoring
4. Configure service discovery and load balancing
5. Implement proper security measures

## Contributing

1. Follow the existing code structure and patterns
2. Use type hints and proper documentation
3. Test changes with the test script
4. Ensure all services are properly integrated

## License

This project is part of a bachelor's thesis implementation.
